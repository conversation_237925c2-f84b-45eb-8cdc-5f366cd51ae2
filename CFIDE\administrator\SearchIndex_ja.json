[{"Category": "サーバーの設定", "SubCategory": "設定", "Contents": "                                                                                                                                                                      サーバーの設定 > 設定            リクエストタイムアウト 秒          チェックボックスをオンにすると、指定の時間を超えたリクエストは終了されます。これにより、非常に長いリクエストがサーバーリソースを占有したり、他のリクエストのパフォーマンスに悪影響を与えないようにすることができます。             アプリケーションごとの設定の有効化        オンにすると、アプリケーションごとの設定がサーバー全体で有効になります。オフにすると、アプリケーションごとの設定がサーバー全体で無効になります。              cftoken 用の UUID の使用         クライアントとセッション変数 cftoken の値用に、乱数ではなく UUID を使用するように設定します。UUID では、トークン用に固有の識別子が生成されます。                   HTTP ステータスコードの有効化         ColdFusion エラーがブラウザに返された場合に、ColdFusion は HTTP エラーステータスコードを設定します。テンプレートが見つからない場合はエラーステータスコード 404 が設定され、サーバーエラーの場合はエラーステータスコード 500 が設定されます。                                                                      空白抑制の有効化                                                                         ColdFusion が CFML ソースファイルによって出力するかもしれない多くの余分な空白、タブ、改行を取り除き、        ColdFusion からブラウザーに返されるページのサイズを縮小します。                                      CFC 型チェックの無効化         オンにすると、CFC 型の UDF 引数が検証されません。引数は \"ANY\" という型として処理されます。この設定は本番環境でのみ使用します。                ColdFusion Java 内部コンポーネントへのアクセスの無効化         ColdFusion の内部実装の一部である Java オブジェクトに対するアクセスと作成を CFML コードが行えないようにします。この設定により、認証されていない CFML テンプレートでは、このサーバーの管理情報や設定情報の読み込みや修正ができなくなります。              シリアル化用の構造体キーで大文字小文字が保持されます。         構造体のキーを定義済みの場合は、大文字小文字が保持されます。オンになっていない場合は、キー     が大文字に変換されます。              シリアル化 JSON への接頭辞付加          カスタム接頭辞を使用してシリアル化 JSON 文字列に接頭辞を付加することで、JSON データを返す Web サービスをクロスサイトスクリプティング攻撃から保護します。                 最大出力バッファーサイズ   KB         各リクエストに対する最大出力バッファーサイズ (KB 単位) です。リクエストの出力サイズがこの制限を超えると、自動的にフラッシュされます。そのような場合、応答はキャッシュできません。              メモリ内ファイルシステムの有効化         メモリ内ファイルシステムのサポートを有効化します             メモリ内仮想ファイルシステムのメモリ制限     MB         メモリ内仮想ファイルシステムのメモリ制限を MB 単位で指定します。                 メモリ内仮想ファイルシステムのアプリケーションごとのメモリ制限     MB         メモリ内仮想ファイルシステムのアプリケーションごとのメモリ制限を MB 単位で指定します。            設定ファイルの変更の確認 (間隔 :    秒        設定ファイルを監視し、変更された場合は自動的にリロードします。Websphere ND 垂直クラスタに ColdFusion をデプロイする場合には、複数のインスタンスの ColdFusion が同じ設定ファイルを共有するので、この設定が必要になります。ほとんどのインストールでは、この機能を有効にしないでください。              グローバルなスクリプト保護         Form、URL、CGI、Cookie などのスコープ変数をクロスサイトスクリプティング攻撃から保護するかどうかを指定してください。                  AttributeCollection での追加属性の許可         ColdFusion タグで attributecollection 構造体に非標準の属性を渡せるかどうかを指定してください。              名前なしアプリケーションの作成を無効にする          Application.cfm または Application.cfc にアプリケーション名が指定されていない場合にアプリケーションの作成を許可しません。              サーブレットコンテキストへのアプリケーション変数の追加を許可          サーブレットコンテキストにアプリケーション変数を追加する必要がある場合に指定します。無効にすると、JSP / サーブレットの相互運用のために、名前なしアプリケーションのデータのみ追加されます。              ORM 検索インデックスディレクトリ                ORM 検索のインデックスファイルを格納するための絶対パスを指定してください。          デフォルト ScriptSrc ディレクトリ           cfform.js ファイルが含まれているディレクトリへのデフォルトのパス (Web ルートからのパス) を指定してください。          Google Map API キー           Google Map API のライセンスキーを指定します。                       onServerStart( ) メソッドを含むコンポーネント            onServerStart() メソッドを含む CFC の絶対パス (例 : \"c:\\server.cfc\") を指定します。または、Web ルートから下の CFC パスをドット区切りで指定します (例 : \"a.b.server\")。デフォルトでは、ColdFusion は Web ルートから下で server.cfc を検索します。               null 値サポートの有効化         ColdFusion で null サポートを有効にするかどうかを指定します。有効にした場合、null 値は空の文字列に変換されません。                      Java を正規表現エンジンとして使用         正規表現の処理にデフォルトの Java RegEx エンジンを使用するかどうかを指定します。このオプションが選択されていない場合は、Perl 互換のエンジンが使用されます。正規表現エンジンを変更するには、ColdFusion テンプレートの再コンパイルが必要になる可能性があります。        並列関数のデフォルトの最大スレッド数     並列関数に使用されるスレッドのデフォルトの最大数を指定します。並列処理をサポートしている ColdFusion 関数では、maxThreads 引数が指定されない場合、この値をデフォルトとして使用します。       CFInclude タグで許可されるファイル拡張子       CFInclude タグ内で使用した場合にコンパイルされるファイル拡張子をカンマ区切りリストとして指定します。    CFFile アップロードに対してブロックされるファイル拡張子        CFFile タグを使用してファイルをアップロードする際にブロックされるファイル拡張子をカンマ区切りリストとして指定します。  * (ワイルドカード) の場合は、すべてのファイルアップロードをブロックすることを示し、:no-extension の場合は、ファイル拡張子のないファイルのアップロードをブロックすることを示します。      Content Security Policy Nonce    If enabled, the Content-Security-Policy header will be sent along with a random generated nonce.         Application.cfc および Application.cfm の検索順序                    デフォルトの順序           Web ルートまで           Web ルート内          Application.cfc および Application.cfm が現在のフォルダで見つからない場合に ColdFusion で使用する検索オプションを指定します。デフォルトでは、ColdFusion はシステムルートまで検索します。         エグゼキュータープール設定 (非同期フレームワーク)              コアプールサイズ     コアプールサイズはキープアライブするワーカースレッドの最小数です。     最大プールサイズ     プールで使用可能にできるスレッドの最大数。       キープアライブ時間     MilliSeconds  稼動待ちのアイドルスレッドのタイムアウト (ミリ秒単位)。プール内のスレッド数がコアプールサイズを超えている場合に、このタイムアウトが使用されます。            Cloud Services スレッドプール設定              コアプールサイズ     コアプールサイズはキープアライブするワーカースレッドの最小数です。     最大プールサイズ     プールで使用可能にできるスレッドの最大数。       キープアライブ時間     MilliSeconds  稼動待ちのアイドルスレッドのタイムアウト (ミリ秒単位)。プール内のスレッド数がコアプールサイズを超えている場合に、このタイムアウトが使用されます。            エラーハンドラ            見つからないテンプレートハンドラ      リクエストされたテンプレートが見つからない場合に実行するテンプレートへの相対パスを指定してください。        サイト全体のエラーハンドラ      リクエストの処理中にエラーが発生した場合に実行するテンプレートへの相対パスを指定してください。            ZIP ファイル設定            最大解凍率    CFZIP を使用してファイルを解凍する場合、例えば比率が 5 であれば、解凍ファイルサイズの上限は元の圧縮ファイルの 5 倍になることを意味します。ファイルの最大解凍率または最大解凍サイズに達したら、解凍操作は停止します。              リクエストサイズの制限              POST リクエストパラメーターの最大数     サーバーに送信される POST リクエスト内のパラメーターの最大数。POST パラメーターの数が指定した最大数を上回る場合、ColdFusion はリクエストを拒否します。     送信データの最大サイズ     MB  単一のリクエストでサーバーに送信できるデータ量を制限します。指定された制限よりも大きいリクエストは ColdFusion によって拒否されます。       リクエストのスロットルしきい値     MB  指定された制限よりも小さいリクエストはスロットルによって処理されません。     リクエストのスロットルメモリ     MB  スロットルの合計メモリサイズを制限します。使用可能な合計メモリが十分でない場合、リクエストはキューに入れられます。この制限よりも大きなリクエストは処理されません。            API Manager           REST 確認を許可    ColdFusion にパブリッシュされた REST サービスを API Manager で確認できるようにするかどうかを指定します。                         © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/server_settings.cfm"}, {"Category": "サーバーの設定", "SubCategory": "リクエストの調整", "Contents": "                                                                                                                                                                     サーバーの設定 > リクエストの調整         リクエストの制限          同時テンプレートリクエストの最大数     同時に処理できる CFML ページリクエスト数。この設定を使用して、負荷の大きいアプリケーションのシステムパフォーマンスを全体的に向上させてください。指定の制限数を超えるリクエストはキューに入れられます。     同時 Web サービスリクエストの最大数     同時に処理できる Web サービスリクエスト数。      同時 CFC 関数リクエストの最大数     HTTP 経由で同時に処理できる ColdFusion コンポーネントのメソッド数。この設定は、CFML 内からの CFC メソッドの呼び出しには影響せず、HTTP 経由でリクエストされたメソッドにのみ影響します。            タグ制限の設定          同時レポートスレッドの最大数      同時に処理できる ColdFusion レポートの最大数。    CFTHREAD に使用できるスレッドの最大数      同時に実行される CFTHREAD によって作成されるスレッドの最大数。CFTHREAD によって作成されるスレッドがこの設定を超える場合は実行待ちになります。この変更を有効にするには、ColdFusion サーバーを再起動する必要があります。            キューのタイムアウト設定            キューで実行待ちのリクエストのタイムアウト   秒    キューにあるリクエストがこの時間を過ぎて実行待ちである場合、リクエストをタイムアウトします。この値は、[リクエストのタイムアウト] の設定 (現在は 60 秒) 以上に設定してください。        リクエストキューのタイムアウトページ      テンプレートリクエストが実行されずにタイムアウトした場合にクライアントに送信する HTML ページの、Web ルートからの相対パスを指定します (例 : /CFIDE/timeout.html)。CFML を含むページは指定できません。このページを指定しない場合、リクエストが実行されなかったときには 500 Request Timeout エラーがクライアントに返されます。                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/limits.cfm"}, {"Category": "サーバーの設定", "SubCategory": "キャッシュ機能", "Contents": "                                                                                                                                  サーバーの設定 > キャッシュ機能        キャッシュされるテンプレートの最大数     テンプレートキャッシングを使用して、キャッシュされるテンプレートの数を制限します。キャッシュが小さい値に設定されていると、テンプレートが再処理される場合があります。サーバーに十分なメモリ容量がある場合は、この値を ColdFusion のすべてのテンプレートの合計数に設定することによって、最適なパフォーマンスを得ることができます。キャッシュを大きい値に設定しても、利用可能なメモリが自動的に減少するわけではありません。これは ColdFusion がテンプレートを増分的にキャッシュするためです。          サーバー全体のキャッシュエンジン             サーバー全体でデフォルトで使用されるキャッシュエンジン。 キャッシュエンジンを切り替えると、古いキャッシュデータが削除されます。             EHCache    JCS     Redis      Memcached             Memcached キャッシュ設定              「localhost:11211,localhost:11212」のように、memcached サーバーの IP アドレスをカンマ区切りで指定します                                 Memcached サーバー                                                                                          Memcached 用に、サーバーレベルのキャッシュプロパティを指定します。これらの設定を変更する場合は、ColdFusion を再起動する必要があります。                                        最大アイドル時間 (秒)                                                            最大持続時間 (秒)                                                            最大要素数                                                            永久                               Redis キャッシュ設定                キャッシュ用に Redis サーバー設定を指定します。キャッシュ用のこれらの設定は、Redis がサーバーレベルでデフォルトのキャッシュエンジンとして選択されている場合、またはアプリケーションレベルのキャッシュエンジンとして Redis を指定した場合のみ適用されます。                            Redis サーバー                                       Redis サーバーのポート                                       パスワード                                                         クラスタ                                                        Is SSL Enabled                                                                                               Redis 用に、サーバーレベルのキャッシュプロパティを指定します。これらの設定を変更する場合は、ColdFusion を再起動する必要があります。                                   最大アイドル時間 (秒)                                                            最大持続時間 (秒)                                                            最大要素数                                                            永久                               JCS キャッシュ設定                                                                                                       JCS JDBC 補助キャッシュによって使用されるテーブルを作成するデータソースを選択します。                                                                Select data source                                                                                                                                                                                            テーブルが既に存在する場合はオーバーライド                                                                                                                 JDBC ディスクキャッシュは、MySQL などのリレーショナルデータベースを永続ストアとして使用します。キャッシュ要素はシリアル化され、BLOB に書き込まれます。詳しくはこちらをごらんください :  JCS JDBC ディスクキャッシュ       JCS 用に、サーバーレベルのキャッシュプロパティを指定します。これらの設定を変更する場合は、ColdFusion を再起動する必要があります。                                      最大アイドル時間 (秒)                                                            最大持続時間 (秒)                                                            最大要素数                                                            永久                                   信頼できるキャッシュ         これをオンにすると、テンプレートキャッシュに現在常駐させるために検出されたリクエストファイルは、潜在的な更新に対して検査されません。サーバーの有効期間中にテンプレートが更新されないサイトに対しては、これによってファイルシステムのオーバーヘッドを最小限に抑えることができます。この設定では、サーバーを再起動する必要はありません。              要求にテンプレートをキャッシュ         オンにすると、要求されたファイルが更新されていないかどうかが 1 回の要求の間に一度だけ確認されます。オフにすると、   同じ要求の中でアクセスされるたびに、要求されたファイルが変更されていないかどうかが確認されます。1 回の要求の間に   テンプレートやコンポーネントに更新が反映される可能性がないアプリケーションの場合は、こうすることによりファイルシステムのオーバーヘッドを最小限に抑えることができます。この設定を変更する場合、サーバーの再起動は不要です。              コンポーネントキャッシュ         このチェックボックスをオンにすると、解決済みのコンポーネントパスがキャッシュされ、再解決が行われなくなります。この設定を変更する場合、サーバーの再起動は不要です。                      クラスファイルの保存         このオプションを選択すると、ColdFusion により生成されるクラスファイルはディスクに保存され、サーバーの再起動後に再使用されます。このオプションは本番システムに適しています。開発中は、このオプションを選択しないようにしてください。              Web サーバーパスのキャッシュ         Web サーバーの埋め込みインストールおよび複数サイトへのインストールの場合にページパスをキャッシュします。これによりパフォーマンスが向上します。               内部キャッシュを使用してクエリを保存する         チェックを付けた場合、キャッシュされたクエリを保存するためにサーバーレベルの内部キャッシュが使用されます。デフォルトでは、キャッシュされたクエリは Ehcache によってサポートされる QUERY 領域に保存されます。           キャッシュされるクエリーの最大数    サーバーで維持するキャッシュクエリーの最大数を制限します。キャッシュクエリーでは、データベーストランザクションからではなくメモリから結果セットを取り出すことができます。クエリーはメモリ内に常駐しており、クエリー結果セットのサイズは異なるので、キャッシュされるクエリーの数に対してユーザーが指定した制限が必要となります。この値を超えると、最も古いクエリーがキャッシュから削除され、指定されたクエリーと置き換えられます。この設定は、アプリケーション固有のキャッシュには適用されません。      テンプレートキャッシュをクリアするには、このボタンをクリックします。次回リクエストされたときにテンプレートはメモリ内にリロードされ、修正されていた場合は再コンパイルされます。    フォルダー固有のテンプレートキャッシュをクリア  選択したフォルダーのテンプレートキャッシュをクリアするには、「特定のフォルダーのテンプレートキャッシュをクリア」をクリックします。テンプレートは、次に必要になったときにメモリに読み込まれ、変更されている場合は再コンパイルされます。          フォルダーを選択                                                コンポーネントキャッシュをクリアするには、このボタンをクリックします。解決済みのコンポーネントパスは無視され、再度解決が試みられます。      クエリキャッシュをクリアするには、このボタンをクリックします。      クエリキャッシュをクリアするには、このボタンをクリックします。                                                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/caching.cfm"}, {"Category": "サーバーの設定", "SubCategory": "クライアント変数", "Contents": "                                                                                                                                          サーバーの設定 > クライアント変数      クライアント変数を使用して、セッション間のユーザー情報と基本設定を保管できます。この Administrator 設定は、clientStorage 属性が cfapplication タグで指定されておらず、clientStorage 変数が Application.cfc で設定されていない場合にのみ使用されます。使用可能なクライアントストレージメカニズムのリストに ColdFusion データソースを追加する場合は、ドロップダウンリストからデータソースを選択し、「追加」をクリックします。データソースをデフォルトのストレージメカニズムとして設定する場合は、ラジオボタンを選択し、「適用」をクリックします。                          クライアントストアとして追加するためのデータソースの選択                                                                 cfartgallery                 cfbookclub                 cfcodeexplorer                 cfdocexamples                                                                                          クライアントセッション用のデフォルトのストレージメカニズムを選択                                                       アクション                    ストレージ名                    説明                                                                                 Cookie                           クライアントベースのテキストファイル。                                                                                                                    Registry                                           システムレジストリ                                                                   なし                                                                                                       破棄の間隔                                                         このオプションは、ColdFusion がクライアントストアで破棄オペレーションを実行する頻度を制御します。クライアントストアを破棄するように設定してある場合は、これはオペレーションが実行されるレートです。デフォルトでは 1 時間 7 分になります。30 分未満にはしないでください。                                                            時間           分                                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/clientvariables.cfm"}, {"Category": "サーバーの設定", "SubCategory": "メモリ変数", "Contents": "                                                                                                                              サーバーの設定 > メモリ変数  ColdFusion サーバーを再起動すると、アプリケーション変数が期限切れになります。ユーザーのセッションが終了すると、セッション変数が期限切れになります。このページや、cfapplication タグまたは Application.cfc で指定するタイムアウト期間の経過後は、どちらのタイプの変数も期限切れになります。     J2EE セッション変数の使用     アプリケーション変数の有効化        セッション変数を有効化  (チェック解除の場合 CSRF 保護は無効)          セッションストレージ設定             以下の設定は、ColdFusion サーバーがセッション変数を保存する場所を制御します。これらの設定は、J2EE セッション変数を使用するオプションが有効になっていない場合にのみ適用されます。                      セッションストレージ                              In Memory       Redis                                   Redis サーバー                                       Redis サーバーのポート                                       パスワード                                                   Redis サーバーのタイムアウト (ミリ秒)                                                   Is SSL Enabled                                                                           Use redis for CFLogin                                                                                         注意 :  セッションストレージを変更するには、ColdFusion アプリケーションサーバーを再起動する必要があります。         最大タイムアウト             これらの変数は、cfapplication タグで使用できるタイムアウトの最大期限を指定します。                           アプリケーション変数                          日                           時間                           分                           秒                         セッション変数                          日                           時間                           分                           秒                         デフォルトのタイムアウト             アプリケーション特有の値が cfapplication タグ内で指定されていない場合は、これらの値は、ColdFusion が使用するタイムアウト期間を指定します。                           アプリケーション変数                          日                           時間                           分                           秒                         セッション変数                          日                           時間                           分                           秒                             セッション Cookie 設定             次の ColdFusion セッション Cookie のプロパティを、サーバーレベルとアプリケーションレベルの両方で設定できます。スクリプト経由で Cookie にアクセスできないようにするには、HTTPOnly を確認してください。暗号化された (HTTPS) 接続でのみ Cookie を使用できるようにするには、セキュア Cookie を確認してください。                       Cookie タイムアウト                                           分                          HTTPOnly                                           セキュア Cookie                                           ColdFusion のタグ / 関数を使用した ColdFusion の内部 Cookie の更新を無効にする                                           Cookie の SameSite のデフォルト値                             -       Strict       Lax       None                                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "settings/memoryvariables.cfm"}, {"Category": "サーバーの設定", "SubCategory": "マッピング", "Contents": "                                                                                                                                     サーバーの設定 > マッピング  ColdFusion マッピングにより、cfinclude タグと cfmodule タグは Web ルートの外にあるページにアクセスできます。これらのタグのマッピングの論理パスで開始するパスを指定すると、ColdFusion はマッピングのディレクトリパスを使用してページを探します。   また、ColdFusion はマッピングを使用して ColdFusion コンポーネント (CFC) も探します。cfinvoke タグと cfobject タグおよび CreateObject 関数は、マップされたディレクトリ内で CFC を探します。    メモ : このマッピングは、Web サーバー仮想ディレクトリとは無関係です。仮想ディレクトリを作成して、URL を介して該当するディレクトリにアクセスする場合は、Web サーバーのマニュアルを参照してください。         ColdFusion マッピングの追加 / 編集                              論理パス                                         ディレクトリパス                                                                             アクティブな ColdFusion マッピング                            アクション                   論理パス                    ディレクトリパス                                         /CFIDE                /Applications/ColdFusion2025/cfusion/wwwroot/CFIDE                                                                                /gateway                /Applications/ColdFusion2025/cfusion/gateway/cfc                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/mappings.cfm"}, {"Category": "サーバーの設定", "SubCategory": "メール", "Contents": "                                                                                                                               サーバーの設定 > メール                                                メールサーバーの設定                             メールサーバー                                    SMTP メールメッセージの送信に使用するサーバーを指定します。メールサーバーのインターネットアドレス (mail.company.com など) または IP アドレス (127.0.0.1 など) を指定できます。                               ユーザー名                                                     パスワード                                                           ご使用のメールサーバーで認証が必要な場合は、使用するユーザー名とパスワードを指定できます。                                        メールに署名                                  メールに ColdFusion の署名を追加する場合は、このチェックボックスを選択します。                                                                キーストア                                                                \"キーストアの場所\" - 秘密キーと証明書を保存するキーストア。サポートされているタイプは JKS (java キーストア) と pkcs12 です。                                             キーストアパスワード                             キーストアのパスワード。                     キーエイリアス                                   キーストアに証明書と秘密キーを保存するときに使用するキーのエイリアス。これが指定されていない場合は、キーストアの最初のエントリが使用されます。                     キーパスワード                                 秘密キーを保存するときに使用するパスワード。これが指定されていない場合は、キーストアパスワードがキーパスワードの代わりに使用されます。                                                                                            メールサーバーの接続の確認           このフォームを送信するときに、このメールサーバーに接続できることを ColdFusion で確認する場合は、このチェックボックスを選択してください。                   サーバーポート                 メールサーバーのデフォルトポート番号です。標準の SMTP メールサーバーポートは 25 です。                                             バックアップメールサーバー                      SMTP メールメッセージの送信に使用するバックアップサーバーを指定します。複数のサーバー名を指定する場合は    カンマで区切ります。デフォルト以外のポート番号を指定するには、    コロンを使用します (例 : mail.company.com:123)。メールサーバーで    デフォルトサーバー以外からの認証が必要な場合は、             user:<EMAIL>  の形式でユーザー名とパスワードを指定できます。                                                                                                                                  メールサーバーへの接続を維持します             メッセージの配達後、メールサーバーへの接続を閉じずに再利用する場合は、このチェックボックスを選択してください (推奨)。                             接続タイムアウト (秒)                      ColdFusion がメールサーバーからの応答を待つ時間を指定します。                                 メールサーバーへの SSL ソケット接続の有効化             メールサーバーへの接続で SSL 暗号化を有効にする場合は、このチェックボックスをオンにします。                                                                                                                   メールサーバーへの TLS 接続の有効化             メールサーバーへの接続で TLS (Transport Level Security) を有効にする場合は、このチェックボックスをオンにします。                                            メールスプールの設定                                       スプール間隔 (秒)                       スプールされたメールをメールスプーラが処理するために待つ時間を指定します。                                配達されるメールメッセージをスプールします               配達するメッセージをメールスプーラにスプールさせる場合は、このチェックボックスを選択します (推奨)。選択しない場合、メールスプーラはページの処理中にメッセージを配達しようとします。                                           未配達メールの添付ファイルのダウンロードを許可します。               未配達メールの添付ファイルのダウンロードを許可するには、このチェックボックスをオンにしてください。未配達メール機能を使用していない場合は、このオプションを無効にすることをお勧めします。                                                                                                                       メールロギングの設定                                         エラーログの厳格度                   デバッグ            情報            警告            エラー                    ロギングする SMTP 関連のエラーメッセージのタイプを選択してください。                             ColdFusion から送信されるメールメッセージをすべてロギング           メッセージの送信者、受信者、件名をログファイルに保存する場合は、このチェックボックスを選択してください。                                                              メール文字セット設定                             デフォルト CFMail 文字設定                         Big5         (Traditional Chinese)              GB2312         (Simplified Chinese)              ISO-2022-JP         (Japanese)              ISO-2022-KR         (Korean)              ISO-8859-13         (Latin-7, Baltic)              ISO-8859-1         (Latin-1, West European)              ISO-8859-2         (Latin-2, Central/East European)              ISO-8859-3         (Latin-3, South European)              ISO-8859-4         (Latin-4, North European)              ISO-8859-5         (Cyrillic)              ISO-8859-6         (Arabic)              ISO-8859-7         (Greek)              ISO-8859-8         (Hebrew)              ISO-8859-9         (Latin-5, Turkish)              US-ASCII        UTF-8         (8-Bit Unicode Transformation Format)                                                                                         © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "mail/index.cfm"}, {"Category": "サーバーの設定", "SubCategory": "スケジュールされたタスク", "Contents": "                                                                                                                           サーバー設定／スケジュールされたタスク         スケジュールされたタスクにより、動的データソースから静的 Web ページを作成できます。また、タスクをスケジュールして、Solr 検索を更新し、報告を作成することもできます。       すべてのタスクを一時停止  すべてのタスクを再開                 サーバーレベルでスケジュールされたタスク                              サーバーレベルのタスクがスケジュールされていません。                                           アプリケーションレベルでスケジュールされたタスク                              アプリケーションレベルのタスクがスケジュールされていません。                             クラスタ設定を有効にする                                    現在、クラスタ設定に対してサポートされているデータベースは、MySQL、Microsoft SQL Server、Oracle です                           現在、クラスタ設定は無効です                                                                                                   データソースを選択                                                                                                                                         クラスタ設定用のテーブルを作成                                                                このオプションは、クラスタ内の 1 つのノードに対してのみ有効にしてください。そうしない場合、テーブルがオーバーライドされます。1 つのノードからテーブルを作成すると、他のノードではデータソースの選択のみを行います。すべてのノードが同じデータソースを参照することで、クラスタが構成されます。                                                                                    © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "scheduler/scheduletasks.cfm"}, {"Category": "サーバーの設定", "SubCategory": "WebSocket", "Contents": "                                                                                                                                サーバー設定／WebSocket     WebSocket サービスを有効化                             プロキシを使用            ColdFusion WebSocket リクエストは外部 Web サーバーが設定された WebSocket プロキシモジュールによって処理されます。設定を有効にするには ColdFusion を再起動してください。                            内蔵 WebSocket サーバーを使用            ColdFusion WebSocket リクエストは内蔵 ColdFusion WebSocket サーバーによって処理されます。設定を有効にするには ColdFusion を再起動してください。                                  ポート                 WebSocket サーバーが受信するすべての ColdFusion WebSocket リクエストをリスンするポートです。設定を有効にするには ColdFusion を再起動してください。                                  SSL ポート                       WebSocket サーバーがセキュアな通信 (SSL) をリスンするポートです。設定を有効にするには ColdFusion を再起動してください。                   キーストア                  キーストアの場所。秘密キーと証明書を保存するキーストア。サポートされているタイプは JKS (Java キーストア) と pkcs12 です。                             キーストアパスワード                      キーストアを開いて公開 / 秘密キーを読み込むために使用するパスワード。                            最大データサイズ                      KB              送受信されるデータパケットの最大サイズ。                                     Flash Policy Server を起動              クライアント側にネイティブな WebSocket サポートがない場合に Flash フォールバックを有効にします。                                     WebSocket クラスタを有効化                       マルチキャストポート                               ノードを上位または下位に移動するイベントをブロードキャストするために WebSocket クラスタが使用するポートです。設定を有効にするには、ColdFusion を再起動してください。                                                                                        © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/websocket.cfm"}, {"Category": "サーバーの設定", "SubCategory": "チャート", "Contents": "                                                                                                                               サーバーの設定 > チャート       キャッシュタイプ       メモリキャッシュ  ディスクキャッシュ  チャートをメモリまたはディスクにキャッシュできます。メモリ内では、キャッシュ機能は迅速ですが、メモリを大量に使用します。     キャッシュされるイメージの最大数      キャッシュに保管するチャートの最大数を指定します。キャッシュがいっぱいになると、新規のチャートが生成されるたびに、最も古いチャートがキャッシュから削除されます。チャートのキャッシュ機能により、同じグラフの生成が一度ですむため、そのチャートへの複数のリクエストがより迅速になります。       各チャートの有効期間 (秒)      生成したチャートがローカルディスクから削除されるまでの時間 (秒) を指定します。このオプションを使用すると、単一のリクエストで多数のチャートが生成される場合に、チャート画像の期限切れを回避できます。     ディスクキャッシュの位置      ディスクへキャッシュする場合は、生成されたチャートを保存するディレクトリを指定します。                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/charting.cfm"}, {"Category": "サーバーの設定", "SubCategory": "フォント管理", "Contents": "                                                                                                                                      サーバー設定 > フォント管理         新規フォントを ColdFusion で登録                              新規フォント / フォントディレクトリ                                                          現在のシステムフォント                                       フォントファミリー                   フォントスタイル                   ポストスクリプト名                   フォントタイプ                    使用対象                   パス                                                   adobe clean han black                  Adobe Clean Han Black                  AdobeCleanHan-Black                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Black.otf                                                                         adobe clean han bold                  Adobe Clean Han Bold                  AdobeCleanHan-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Bold.otf                                                                         adobe clean han extrabold                  Adobe Clean Han ExtraBold                  AdobeCleanHan-ExtraBold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-ExtraBold.otf                                                                         adobe clean han extralight                  Adobe Clean Han ExtraLight                  AdobeCleanHan-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-ExtraLight.otf                                                                         adobe clean han light                  Adobe Clean Han Light                  AdobeCleanHan-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Light.otf                                                                         adobe clean han normal                  Adobe Clean Han Normal                  AdobeCleanHan-Normal                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Normal.otf                                                                         adobe clean han regular                  Adobe Clean Han Regular                  AdobeCleanHan-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Regular.otf                                                                         adobeclean-bold                  Adobe Clean Bold                  AdobeClean-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Bold.otf                                                                         adobeclean-bold                  AdobeClean-Bold                  AdobeClean-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Bold.otf                                                                         adobeclean-boldit                  Adobe Clean Bold Italic                  AdobeClean-BoldIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldIt.otf                                                                         adobeclean-boldit                  AdobeClean-BoldIt                  AdobeClean-BoldIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldIt.otf                                                                         adobeclean-boldsemicn                  AdobeClean-BoldSemiCn                  AdobeClean-BoldSemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCn.otf                                                                         adobeclean-boldsemicn                  Adobe Clean Bold SemiCondensed                  AdobeClean-BoldSemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCn.otf                                                                         adobeclean-boldsemicnit                  AdobeClean-BoldSemiCnIt                  AdobeClean-BoldSemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCnIt.otf                                                                         adobeclean-boldsemicnit                  Adobe Clean Bold SemiCondensed Italic                  AdobeClean-BoldSemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCnIt.otf                                                                         adobeclean-it                  Adobe Clean Italic                  AdobeClean-It                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-It.otf                                                                         adobeclean-it                  AdobeClean-It                  AdobeClean-It                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-It.otf                                                                         adobeclean-light                  AdobeClean-Light                  AdobeClean-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Light.otf                                                                         adobeclean-light                  Adobe Clean Light                  AdobeClean-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Light.otf                                                                         adobeclean-lightit                  Adobe Clean Light Italic                  AdobeClean-LightIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-LightIt.otf                                                                         adobeclean-lightit                  AdobeClean-LightIt                  AdobeClean-LightIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-LightIt.otf                                                                         adobeclean-regular                  Adobe Clean                  AdobeClean-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Regular.otf                                                                         adobeclean-regular                  AdobeClean-Regular                  AdobeClean-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Regular.otf                                                                         adobeclean-semicn                  Adobe Clean SemiCondensed                  AdobeClean-SemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCn.otf                                                                         adobeclean-semicn                  AdobeClean-SemiCn                  AdobeClean-SemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCn.otf                                                                         adobeclean-semicnit                  Adobe Clean SemiCondensed Italic                  AdobeClean-SemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCnIt.otf                                                                         adobeclean-semicnit                  AdobeClean-SemiCnIt                  AdobeClean-SemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCnIt.otf                                                                         apple braille                  Apple Braille                  AppleBraille                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille.ttf                                                                         apple braille                  Apple Braille Outline 8 Dot                  AppleBraille-Outline8Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Outline 8 Dot.ttf                                                                         apple braille                  Apple Braille Pinpoint 8 Dot                  AppleBraille-Pinpoint8Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Pinpoint 8 Dot.ttf                                                                         apple braille                  Apple Braille Pinpoint 6 Dot                  AppleBraille-Pinpoint6Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Pinpoint 6 Dot.ttf                                                                         apple braille                  Apple Braille Outline 6 Dot                  AppleBraille-Outline6Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Outline 6 Dot.ttf                                                                         apple color emoji                  Apple Color Emoji                  AppleColorEmoji                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Color Emoji.ttc,0                                                                         apple symbols                  Apple Symbols                  AppleSymbols                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Symbols.ttf                                                                         arial hebrew                  Arial Hebrew Bold                  ArialHebrew-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/ArialHB.ttc,1                                                                         arial hebrew                  Arial Hebrew Light                  ArialHebrew-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/ArialHB.ttc,2                                                                         arial hebrew                  Arial Hebrew                  ArialHebrew                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/ArialHB.ttc,0                                                                         arial unicode ms                  Arial Unicode MS                  ArialUnicodeMS                  TRUETYPE                  PDF/FlashPaper                                                  /Library/Fonts/Arial Unicode.ttf                                                                         avenir                  Avenir Roman                  Avenir-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,11                                                                         avenir                  Avenir Oblique                  Avenir-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,10                                                                         avenir black                  Avenir Black                  Avenir-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,2                                                                         avenir black oblique                  Avenir Black Oblique                  Avenir-BlackOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,3                                                                         avenir book                  Avenir Book                  Avenir-Book                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,0                                                                         avenir book                  Avenir Book Oblique                  Avenir-BookOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,1                                                                         avenir heavy                  Avenir Heavy                  Avenir-Heavy                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,4                                                                         avenir heavy                  Avenir Heavy Oblique                  Avenir-HeavyOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,5                                                                         avenir light                  Avenir Light Oblique                  Avenir-LightOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,7                                                                         avenir light                  Avenir Light                  Avenir-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,6                                                                         avenir medium                  Avenir Medium                  Avenir-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,8                                                                         avenir medium                  Avenir Medium Oblique                  Avenir-MediumOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,9                                                                         avenir next                  Avenir Next Bold Italic                  AvenirNext-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,1                                                                         avenir next                  Avenir Next Bold                  AvenirNext-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,0                                                                         avenir next                  Avenir Next Italic                  AvenirNext-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,4                                                                         avenir next                  Avenir Next Regular                  AvenirNext-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,7                                                                         avenir next condensed                  Avenir Next Condensed Bold                  AvenirNextCondensed-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,0                                                                         avenir next condensed                  Avenir Next Condensed Regular                  AvenirNextCondensed-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,7                                                                         avenir next condensed                  Avenir Next Condensed Bold Italic                  AvenirNextCondensed-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,1                                                                         avenir next condensed                  Avenir Next Condensed Italic                  AvenirNextCondensed-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,4                                                                         avenir next condensed demi bold                  Avenir Next Condensed Demi Bold Italic                  AvenirNextCondensed-DemiBoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,3                                                                         avenir next condensed demi bold                  Avenir Next Condensed Demi Bold                  AvenirNextCondensed-DemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,2                                                                         avenir next condensed heavy                  Avenir Next Condensed Heavy Italic                  AvenirNextCondensed-HeavyItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,9                                                                         avenir next condensed heavy                  Avenir Next Condensed Heavy                  AvenirNextCondensed-Heavy                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,8                                                                         avenir next condensed medium                  Avenir Next Condensed Medium                  AvenirNextCondensed-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,5                                                                         avenir next condensed medium                  Avenir Next Condensed Medium Italic                  AvenirNextCondensed-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,6                                                                         avenir next condensed medium                  Avenir Next Medium Condensed Italic                  AvenirNextCondensed-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,6                                                                         avenir next condensed ultra light                  Avenir Next Condensed Ultra Light                  AvenirNextCondensed-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,10                                                                         avenir next condensed ultra light                  Avenir Next Condensed Ultra Light Italic                  AvenirNextCondensed-UltraLightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,11                                                                         avenir next demi bold                  Avenir Next Demi Bold Italic                  AvenirNext-DemiBoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,3                                                                         avenir next demi bold                  Avenir Next Demi Bold                  AvenirNext-DemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,2                                                                         avenir next heavy                  Avenir Next Heavy Italic                  AvenirNext-HeavyItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,9                                                                         avenir next heavy                  Avenir Next Heavy                  AvenirNext-Heavy                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,8                                                                         avenir next medium                  Avenir Next Medium Italic                  AvenirNext-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,6                                                                         avenir next medium                  Avenir Next Medium                  AvenirNext-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,5                                                                         avenir next ultra light                  AvenirNext-UltraLight                  AvenirNext-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,10                                                                         avenir next ultra light                  Avenir Next Ultra Light                  AvenirNext-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,10                                                                         avenir next ultra light                  Avenir Next Ultra Light Italic                  AvenirNext-UltraLightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,11                                                                         courier                  Courier Bold Oblique                  Courier-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,3                                                                         courier                  Courier-Oblique                  Courier-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,2                                                                         courier                  Courier                  Courier                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,0                                                                         courier                  Courier Oblique                  Courier-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,2                                                                         courier                  Courier-BoldOblique                  Courier-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,3                                                                         courier                  Courier Bold                  Courier-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,1                                                                         courier                  Courier-Bold                  Courier-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,1                                                                         geeza pro                  Geeza Pro Regular                  GeezaPro                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/GeezaPro.ttc,0                                                                         geeza pro                  Geeza Pro Bold                  GeezaPro-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/GeezaPro.ttc,1                                                                         geneva                  Geneva                  Geneva                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Geneva.ttf                                                                         heiti sc                  Heiti SC Light                  STHeitiSC-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Light.ttc,1                                                                         heiti sc                  Heiti SC Medium                  STHeitiSC-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Medium.ttc,1                                                                         heiti tc                  Heiti TC Medium                  STHeitiTC-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Medium.ttc,0                                                                         heiti tc                  Heiti TC Light                  STHeitiTC-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Light.ttc,0                                                                         helvetica                  Helvetica Bold                  Helvetica-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,1                                                                         helvetica                  Helvetica Light                  Helvetica-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,4                                                                         helvetica                  Helvetica Bold Oblique                  Helvetica-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,3                                                                         helvetica                  Helvetica Oblique                  Helvetica-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,2                                                                         helvetica                  Helvetica-BoldOblique                  Helvetica-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,3                                                                         helvetica                  Helvetica-Oblique                  Helvetica-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,2                                                                         helvetica                  Helvetica                  Helvetica                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,0                                                                         helvetica                  Helvetica-Bold                  Helvetica-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,1                                                                         helvetica                  Helvetica Light Oblique                  Helvetica-LightOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,5                                                                         helvetica neue                  Helvetica Neue Bold                  HelveticaNeue-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,1                                                                         helvetica neue                  Helvetica Neue Thin Italic                  HelveticaNeue-ThinItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,13                                                                         helvetica neue                  Helvetica Neue Light                  HelveticaNeue-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,7                                                                         helvetica neue                  Helvetica Neue UltraLight Italic                  HelveticaNeue-UltraLightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,6                                                                         helvetica neue                  Helvetica Neue Italic                  HelveticaNeue-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,2                                                                         helvetica neue                  Helvetica Neue Medium                  HelveticaNeue-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,10                                                                         helvetica neue                  Helvetica Neue Light Italic                  HelveticaNeue-LightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,8                                                                         helvetica neue                  Helvetica Neue Condensed Bold                  HelveticaNeue-CondensedBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,4                                                                         helvetica neue                  Helvetica Neue Thin                  HelveticaNeue-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,12                                                                         helvetica neue                  Helvetica Neue                  HelveticaNeue                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,0                                                                         helvetica neue                  Helvetica Neue Bold Italic                  HelveticaNeue-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,3                                                                         helvetica neue                  Helvetica Neue Condensed Black                  HelveticaNeue-CondensedBlack                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,9                                                                         helvetica neue                  Helvetica Neue UltraLight                  HelveticaNeue-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,5                                                                         helvetica neue                  Helvetica Neue Medium Italic                  HelveticaNeue-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,11                                                                         kozgopr6n-bold                  KozGoPr6N-Bold                  KozGoPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Bold.otf                                                                         kozgopr6n-bold                  Kozuka Gothic Pr6N B                  KozGoPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Bold.otf                                                                         kozgopr6n-extralight                  KozGoPr6N-ExtraLight                  KozGoPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-ExtraLight.otf                                                                         kozgopr6n-extralight                  Kozuka Gothic Pr6N EL                  KozGoPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-ExtraLight.otf                                                                         kozgopr6n-heavy                  KozGoPr6N-Heavy                  KozGoPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Heavy.otf                                                                         kozgopr6n-heavy                  Kozuka Gothic Pr6N H                  KozGoPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Heavy.otf                                                                         kozgopr6n-light                  KozGoPr6N-Light                  KozGoPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Light.otf                                                                         kozgopr6n-light                  Kozuka Gothic Pr6N L                  KozGoPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Light.otf                                                                         kozgopr6n-medium                  Kozuka Gothic Pr6N M                  KozGoPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Medium.otf                                                                         kozgopr6n-medium                  KozGoPr6N-Medium                  KozGoPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Medium.otf                                                                         kozgopr6n-regular                  Kozuka Gothic Pr6N R                  KozGoPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Regular.otf                                                                         kozgopr6n-regular                  KozGoPr6N-Regular                  KozGoPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Regular.otf                                                                         kozminpr6n-bold                  KozMinPr6N-Bold                  KozMinPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Bold.otf                                                                         kozminpr6n-bold                  Kozuka Mincho Pr6N B                  KozMinPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Bold.otf                                                                         kozminpr6n-extralight                  KozMinPr6N-ExtraLight                  KozMinPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-ExtraLight.otf                                                                         kozminpr6n-extralight                  Kozuka Mincho Pr6N EL                  KozMinPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-ExtraLight.otf                                                                         kozminpr6n-heavy                  Kozuka Mincho Pr6N H                  KozMinPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Heavy.otf                                                                         kozminpr6n-heavy                  KozMinPr6N-Heavy                  KozMinPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Heavy.otf                                                                         kozminpr6n-light                  KozMinPr6N-Light                  KozMinPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Light.otf                                                                         kozminpr6n-light                  Kozuka Mincho Pr6N L                  KozMinPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Light.otf                                                                         kozminpr6n-medium                  Kozuka Mincho Pr6N M                  KozMinPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Medium.otf                                                                         kozminpr6n-medium                  KozMinPr6N-Medium                  KozMinPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Medium.otf                                                                         kozminpr6n-regular                  KozMinPr6N-Regular                  KozMinPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Regular.otf                                                                         kozminpr6n-regular                  Kozuka Mincho Pr6N R                  KozMinPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Regular.otf                                                                         lucida grande                  Lucida Grande                  LucidaGrande                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/LucidaGrande.ttc,0                                                                         lucida grande                  Lucida Grande Bold                  LucidaGrande-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/LucidaGrande.ttc,1                                                                         marker felt                  Marker Felt Wide                  MarkerFelt-Wide                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MarkerFelt.ttc,1                                                                         marker felt                  Marker Felt Thin                  MarkerFelt-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MarkerFelt.ttc,0                                                                         menlo                  Menlo Bold Italic                  Menlo-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,3                                                                         menlo                  Menlo Bold                  Menlo-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,1                                                                         menlo                  Menlo Regular                  Menlo-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,0                                                                         menlo                  Menlo Italic                  Menlo-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,2                                                                         monaco                  Monaco                  Monaco                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Monaco.ttf                                                                         muktamahee bold                  MuktaMahee Bold                  MuktaMahee-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,6                                                                         muktamahee extrabold                  MuktaMahee ExtraBold                  MuktaMahee-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,5                                                                         muktamahee extralight                  MuktaMahee ExtraLight                  MuktaMahee-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,4                                                                         muktamahee light                  MuktaMahee Light                  MuktaMahee-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,3                                                                         muktamahee medium                  MuktaMahee Medium                  MuktaMahee-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,2                                                                         muktamahee regular                  MuktaMahee Regular                  MuktaMahee-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,0                                                                         muktamahee semibold                  MuktaMahee SemiBold                  MuktaMahee-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,1                                                                         noteworthy                  Noteworthy Light                  Noteworthy-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Noteworthy.ttc,0                                                                         noteworthy                  Noteworthy Bold                  Noteworthy-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Noteworthy.ttc,1                                                                         noto nastaliq urdu                  Noto Nastaliq Urdu                  NotoNastaliqUrdu                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoNastaliq.ttc,0                                                                         noto sans armenian                  Noto Sans Armenian Bold                  NotoSansArmenian-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,1                                                                         noto sans armenian                  Noto Sans Armenian Regular                  NotoSansArmenian-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,6                                                                         noto sans armenian blk                  Noto Sans Armenian Black                  NotoSansArmenian-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,0                                                                         noto sans armenian extbd                  Noto Sans Armenian ExtraBold                  NotoSansArmenian-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,2                                                                         noto sans armenian extlt                  Noto Sans Armenian ExtraLight                  NotoSansArmenian-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,3                                                                         noto sans armenian light                  Noto Sans Armenian Light                  NotoSansArmenian-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,4                                                                         noto sans armenian med                  Noto Sans Armenian Medium                  NotoSansArmenian-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,5                                                                         noto sans armenian sembd                  Noto Sans Armenian SemiBold                  NotoSansArmenian-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,7                                                                         noto sans armenian thin                  Noto Sans Armenian Thin                  NotoSansArmenian-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,8                                                                         noto sans kannada                  Noto Sans Kannada Bold                  NotoSansKannada-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,6                                                                         noto sans kannada                  Noto Sans Kannada Regular                  NotoSansKannada-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,3                                                                         noto sans kannada black                  Noto Sans Kannada Black                  NotoSansKannada-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,8                                                                         noto sans kannada extrabold                  Noto Sans Kannada ExtraBold                  NotoSansKannada-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,7                                                                         noto sans kannada extralight                  Noto Sans Kannada ExtraLight                  NotoSansKannada-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,1                                                                         noto sans kannada light                  Noto Sans Kannada Light                  NotoSansKannada-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,2                                                                         noto sans kannada medium                  Noto Sans Kannada Medium                  NotoSansKannada-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,4                                                                         noto sans kannada semibold                  Noto Sans Kannada SemiBold                  NotoSansKannada-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,5                                                                         noto sans kannada thin                  Noto Sans Kannada Thin                  NotoSansKannada-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,0                                                                         noto sans myanmar                  Noto Sans Myanmar Bold                  NotoSansMyanmar-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,1                                                                         noto sans myanmar                  Noto Sans Myanmar Regular                  NotoSansMyanmar-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,6                                                                         noto sans myanmar blk                  Noto Sans Myanmar Black                  NotoSansMyanmar-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,0                                                                         noto sans myanmar extbd                  Noto Sans Myanmar ExtraBold                  NotoSansMyanmar-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,2                                                                         noto sans myanmar extlt                  Noto Sans Myanmar ExtraLight                  NotoSansMyanmar-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,3                                                                         noto sans myanmar light                  Noto Sans Myanmar Light                  NotoSansMyanmar-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,4                                                                         noto sans myanmar med                  Noto Sans Myanmar Medium                  NotoSansMyanmar-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,5                                                                         noto sans myanmar sembd                  Noto Sans Myanmar SemiBold                  NotoSansMyanmar-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,7                                                                         noto sans myanmar thin                  Noto Sans Myanmar Thin                  NotoSansMyanmar-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,8                                                                         noto sans oriya                  Noto Sans Oriya                  NotoSansOriya                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansOriya.ttc,0                                                                         noto sans oriya                  Noto Sans Oriya Bold                  NotoSansOriya-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansOriya.ttc,1                                                                         noto sans zawgyi                  Noto Sans Zawgyi Bold                  NotoSansZawgyi-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,10                                                                         noto sans zawgyi                  Noto Sans Zawgyi Regular                  NotoSansZawgyi-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,15                                                                         noto sans zawgyi blk                  Noto Sans Zawgyi Black                  NotoSansZawgyi-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,9                                                                         noto sans zawgyi extbd                  Noto Sans Zawgyi ExtraBold                  NotoSansZawgyi-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,11                                                                         noto sans zawgyi extlt                  Noto Sans Zawgyi ExtraLight                  NotoSansZawgyi-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,12                                                                         noto sans zawgyi light                  Noto Sans Zawgyi Light                  NotoSansZawgyi-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,13                                                                         noto sans zawgyi med                  Noto Sans Zawgyi Medium                  NotoSansZawgyi-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,14                                                                         noto sans zawgyi sembd                  Noto Sans Zawgyi SemiBold                  NotoSansZawgyi-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,16                                                                         noto sans zawgyi thin                  Noto Sans Zawgyi Thin                  NotoSansZawgyi-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,17                                                                         noto serif myanmar                  Noto Serif Myanmar Bold                  NotoSerifMyanmar-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,1                                                                         noto serif myanmar                  Noto Serif Myanmar Regular                  NotoSerifMyanmar-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,6                                                                         noto serif myanmar blk                  Noto Serif Myanmar Black                  NotoSerifMyanmar-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,0                                                                         noto serif myanmar extbd                  Noto Serif Myanmar ExtraBold                  NotoSerifMyanmar-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,2                                                                         noto serif myanmar extlt                  Noto Serif Myanmar ExtraLight                  NotoSerifMyanmar-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,3                                                                         noto serif myanmar light                  Noto Serif Myanmar Light                  NotoSerifMyanmar-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,4                                                                         noto serif myanmar med                  Noto Serif Myanmar Medium                  NotoSerifMyanmar-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,5                                                                         noto serif myanmar sembd                  Noto Serif Myanmar SemiBold                  NotoSerifMyanmar-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,7                                                                         noto serif myanmar thin                  Noto Serif Myanmar Thin                  NotoSerifMyanmar-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,8                                                                         optima                  Optima Italic                  Optima-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,2                                                                         optima                  Optima Bold                  Optima-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,1                                                                         optima                  Optima Regular                  Optima-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,0                                                                         optima                  Optima ExtraBlack                  Optima-ExtraBlack                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,4                                                                         optima                  Optima Bold Italic                  Optima-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,3                                                                         palatino                  Palatino Bold                  Palatino-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,2                                                                         palatino                  Palatino                  Palatino-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,0                                                                         palatino                  Palatino Bold Italic                  Palatino-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,3                                                                         palatino                  Palatino Italic                  Palatino-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,1                                                                         symbol                  Symbol                  Symbol                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Symbol.ttf                                                                         system font                  System Font                  .SFNS-Regular                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/SFNS.ttf                                                                         system font                  System Font Regular Italic                  .SFNS-RegularItalic                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/SFNSItalic.ttf                                                                         times                  Times Bold Italic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times                  Times-Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times                  Times-BoldItalic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times                  Times Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         times                  Times Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times                  Times Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times                  Times-Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times                  Times-Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         times-roman                  Times Bold Italic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times-roman                  Times-Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times-roman                  Times-BoldItalic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times-roman                  Times Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         times-roman                  Times Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times-roman                  Times Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times-roman                  Times-Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times-roman                  Times-Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         zapf dingbats                  Zapf Dingbats                  ZapfDingbatsITC                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/ZapfDingbats.ttf                                                                         zapfdingbats                  ZapfDingbats                  ZapfDingbats                  ADOBE-BUILT-IN                  PDF                                                  ZapfDingbats                                                                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/fonts.cfm"}, {"Category": "サーバーの設定", "SubCategory": "ドキュメント", "Contents": "                                                                                                                               サーバーの設定 > ドキュメント         ColdFusion でのローカル OpenOffice の設定                                                                  OpenOffice ディレクトリ                                                                                           ColdFusion でのリモート OpenOffice の設定                                                                 リモートホスト                                                                                       リモートポート                                                                                                                                          注意 :                         1. OpenOffice をローカルとリモートの両方のホストで設定する場合は、リモートホストで設定される OpenOffice を使用してください。                                                      2. リモート OpenOffice の設定後、ColdFusion を再起動します。                                                             © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/office.cfm"}, {"Category": "サーバーの設定", "SubCategory": "CCS", "Contents": "                                                                                                                                      CCS     CCS クライアント                       現在の環境                  デプロイメントタイプを指定してください。環境ごとに独自の設定になります                                   development              production              beta                              シークレットキー                これは、このノードをセントラル設定サーバーに登録する際に指定する必要があるシークレットキーです。               cefb0fb8-a726-4495-9850-127d7e5bce4a                 CCS クラスター名                このノードが属するクラスターの名前を指定してください。                                 CCS プロトコル                中央管理型サーバーのプロトコル                           有効          無効                         CCS サーバー                セントラル設定サーバーの IP アドレス                                 CCS ポート                   セントラル設定サーバーが稼働しているポート                                 CCS 有効                このノードに対してセントラル設定サーバーが有効になっているか無効になっているか                        有効          無効                       有効にしてプッシュ                                 ステータス                セントラル設定サーバーが稼働しているかどうか                  非稼働         設定のロード元:                    環境                  このノードに対する設定のロード元となるデプロイメントタイプ                                  development              production              beta                                  現在ロードされているバージョン                  このノードにロードする設定を持っている上記指定のデプロイメントの特定バージョン。                              最新バージョン                                                             バージョン履歴:    作成されたバージョンがありません。                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/ccs.cfm"}, {"Category": "サーバーの設定", "SubCategory": "Java と JVM", "Contents": "                                                                                                                               サーバー設定 > Java と JVM     Java 設定と JVM 設定により、ColdFusion が Java 仮想マシンを起動する方法を制御できます。この設定で、使用するクラスパスや割り当てるメモリサイズを制御し、また、任意の JVM 引数を設定できます。この設定を変更した場合は、ColdFusion を再起動する必要があります。不適切な設定を行うと、ColdFusion が正しく起動しなくなる場合があります。  When you select the Submit button, backups of the jvm.config file are created. These backups can be used to restore the file in case of significant changes.  You can locate the jvm.config file at /Applications/ColdFusion2025/cfusion/bin/jvm.config      Java 仮想マシンのパス        Java 仮想マシン (JVM) の位置を指定します。     最小 JVM ヒープサイズ (MB)          最大 JVM ヒープサイズ (MB)         メモリサイズ設定により、JVM がプログラムとデータに使用するメモリ量が決まります。     ColdFusion クラスパス     {application.home}/lib/updates,{application.home}/lib/,{application.home}/gateway/lib/,{application.home}/wwwroot/WEB-INF/cfform/jars,{application.home}/bin/cf-osgicli.jar   JVM 用の追加クラスパスを指定します。複数のディレクトリを指定する場合はカンマで区切ります。     JVM 引数     -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005 --add-exports=java.desktop/sun.awt.image=ALL-UNNAMED --add-exports=java.desktop/sun.java2d=ALL-UNNAMED --add-exports=java.base/sun.security.tools.keytool=ALL-UNNAMED --add-exports=java.base/sun.util.calendar=ALL-UNNAMED --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-opens=java.desktop/java.awt=ALL-UNNAMED --add-opens=java.desktop/java.awt.image=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED --add-opens=java.base/java.lang.ref=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.security.rsa=ALL-UNNAMED --add-opens=java.base/sun.security.pkcs=ALL-UNNAMED --add-opens=java.base/sun.security.x509=ALL-UNNAMED --add-opens=java.base/sun.security.util=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/sun.util.cldr=ALL-UNNAMED --add-opens=java.base/sun.util.locale.provider=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED -XX:+UseParallelGC -Djdk.attach.allowAttachSelf=true -Dcoldfusion.home={application.home} -Duser.language=en -Dcoldfusion.rootDir={application.home} -Dcoldfusion.libPath={application.home}/lib -Dorg.apache.coyote.USE_CUSTOM_STATUS_MSG_IN_HEADER=true -Dcoldfusion.jsafe.defaultalgo=FIPS186Random -Dorg.eclipse.jetty.util.log.class=org.eclipse.jetty.util.log.JavaUtilLog -Djava.util.logging.config.file={application.home}/lib/logging.properties -Dtika.config=tika-config.xml -Djava.locale.providers=COMPAT,SPI -Dsun.font.layoutengine=icu -Dcom.sun.media.jai.disableMediaLib=true -Djdk.lang.Process.allowAmbiguousCommands=true -Duser.language=ja -Dcoldfusion.datasource.blocked.properties=allowLoadLocalInfile,autoDeserialize   特定の JVM 初期化オプションを、スペースで区切って指定します。                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "settings/jvm.cfm"}, {"Category": "サーバーの設定", "SubCategory": "SSL 証明書", "Contents": "                                                                                                                                                                                                   SSL 証明書を設定                 SSL 証明書を使用                  既存の SSL 証明書を指定するか、新規に作成してください。                       既存          作成                            証明書の場所                  SSL 証明書が保存されるファイルパスを指定してください。                                                 パスワード                  SSL 証明書にアクセスするためのパスワードを入力してください。                                          アルゴリズム                  SSL 証明書の暗号化アルゴリズム (例 : RSA) を選択します。                                    UNDEFINED                  RSA                  DSA                  EC                                     HTTPS                  暗号化された安全な通信のための HTTPS プロトコルを許可する場合に選択してください。                          有効                        Http                  暗号化されていない標準的な通信のための HTTP プロトコルを許可する場合に選択してください。                          有効                                            識別名                一般名 (CN)、組織 (O)、場所 (L) などの属性を含んだ、エンティティの一意の識別子。例 : CN=localhost,O=Organization,OU=OrgUnit,L=Location,ST=State,C=Country                                     パスワード                SSL 証明書にアクセスするためのパスワードを入力してください。                                     証明書の名前                SSL 証明書の名前を入力してください。この名前は一意である必要があります。                                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/sslcertificate.cfm"}, {"Category": "サーバーの設定", "SubCategory": "設定要約", "Contents": "                                                                                                                                クリックすると、サーバー設定に関する PDF が新しいウィンドウで生成されます。                                                                        サーバーの設定 > 設定要約   レポート生成日時 : Jan 29, 2025 07:07 PM  このレポートは、すべての ColdFusion 設定のステータスを示します。各項目についての設定を編集することができる ColdFusion Administrator のページを表示する場合は、レポート内の任意のグループ名をクリックしてください。         システム情報                        サーバーの詳細                   サーバー製品            ColdFusion 2025                  バージョン            2025,0,0,331320                  エディション            Developer                      オペレーティングシステム            Mac OS X                    OS バージョン            15.1.1                      Adobe ドライバのバージョン            5.1.4 (Build 0001)                         Tomcat Version               10.1.34.0                        デバイス ID            29c8c3c0d5146c294ef6b2b9615fa0f0e5f419a3a8142b71d361215b5ae9e738                             Package Details                       Installed package(s)            adminapi[2025.0.0.331320]  administrator[2025.0.0.331320]  ajax[2025.0.0.331320]  awsdynamodb[2025.0.0.331320]  awslambda[2025.0.0.331320]  awss3[2025.0.0.331320]  awss3legacy[2025.0.0.331320]  awssns[2025.0.0.331320]  awssqs[2025.0.0.331320]  axis[2025.0.0.331320]  azureblob[2025.0.0.331320]  azureservicebus[2025.0.0.331320]  caching[2025.0.0.331320]  ccs[2025.0.0.331320]  cfmongodb[2025.0.0.331320]  chart[2025.0.0.331320]  db2[2025.0.0.331320]  debugger[2025.0.0.331320]  derby[2025.0.0.331320]  document[2025.0.0.331320]  dotnet[2025.0.0.331320]  eventgateways[2025.0.0.331320]  exchange[2025.0.0.331320]  feed[2025.0.0.331320]  ftp[2025.0.0.331320]  gcpfirestore[2025.0.0.331320]  gcppubsub[2025.0.0.331320]  gcpstorage[2025.0.0.331320]  graphqlclient[2025.0.0.331320]  htmltopdf[2025.0.0.331320]  image[2025.0.0.331320]  mail[2025.0.0.331320]  msgraph[2025.0.0.331320]  mysql[2025.0.0.331320]  oracle[2025.0.0.331320]  orm[2025.0.0.331320]  ormsearch[2025.0.0.331320]  pdf[2025.0.0.331320]  pmtagent[2025.0.0.331320]  postgresql[2025.0.0.331320]  presentation[2025.0.0.331320]  print[2025.0.0.331320]  redissessionstorage[2025.0.0.331320]  report[2025.0.0.331320]  saml[2025.0.0.331320]  scheduler[2025.0.0.331320]  search[2025.0.0.331320]  sharepoint[2025.0.0.331320]  spreadsheet[2025.0.0.331320]  sqlserver[2025.0.0.331320]  websocket[2025.0.0.331320]  zip[2025.0.0.331320]                             JVM 詳細                       Java バージョン            21.0.6                    Java ベンダ            Oracle Corporation                    Java ベンダ URL             https://java.oracle.com/                     Java ホーム            /Applications/ColdFusion2025/jre/Contents/Home                    Java ファイルエンコード            UTF8                    Java デフォルトロケール            ja_IN                    ファイルセパレータ            /                    パスセパレータ            :                    行セパレータ            Chr(10)                    ユーザー名            root                    ユーザーホーム            /var/root                    ユーザーディレクトリ            /Applications/ColdFusion2025/cfusion/bin                    Java VM 仕様バージョン            21                    Java VM 仕様ベンダ            Oracle Corporation                    Java VM 仕様名            Java Virtual Machine Specification                    Java VM バージョン            21.0.6+8-LTS-188                    Java VM ベンダ            Oracle Corporation                    Java VM 名            Java HotSpot(TM) 64-Bit Server VM                    Java 仕様バージョン            21                    Java 仕様ベンダ            Oracle Corporation                    Java 仕様名            Java Platform API Specification                    Java クラスバージョン            65.0                      CF サーバー Java クラスパス            :/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/fluent-hc-4.5.13.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-datatype-jsr310.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-jetty-http-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpclient-cache.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-xjc-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-discovery-0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jutf7-0.9.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/msg-simple-1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/slf4j-reload4j.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/mail.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/certjWithNative.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/json.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cxf-rt-rs-client.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-text-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta-oro-2.0.6.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/hk2-locator-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xsdlib.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/protobuf-java-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-csv.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.mail-api-2.1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-codec.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-server-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ehcache.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ehcache-web-2.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jsr107cache.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/batik-ext.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-pkg-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-servlet-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/serializer.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.activation-2.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-httpclient-3.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-xmp-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-api-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpcore-nio-4.4.14.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bcprov-jdk15on-153.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/activation-1.1.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-impl-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jopt-simple-5.0.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jboss-logging-3.3.0.Final.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/Saxon-HE-9.8.0-10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.inject-api-2.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/closure-compiler.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-configuration-1.10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/avro.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-simple-http-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-zip-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-libs-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xalan.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-xml-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jline-3.13.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-image-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-jakarta-rs-json-provider-2.15.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/json-patch-master-1.10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xml-apis-ext.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-media-jaxb-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/classmate-1.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/slf4j-api.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/js.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpclient.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-audiovideo-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-jdk-http-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-collections4-4.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/management-api-1.1-rev-1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/joda-time-2.8.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ant.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/icu4j-charsetdetect-52_2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-advancedmedia-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parsers-standard-package.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-io.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-api.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jempbox-1.8.16.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-html-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-font-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/batik-css.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-logging.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-mail-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion-req.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/batik-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/log4j.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-ocr-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/esapi.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpcore.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/asm-all-5.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/relaxngDatatype.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-databind.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cxf-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/common-io.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-net.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/restfb.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion-ua.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/btf-1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-guava-2.26-b03.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/protobuf-java.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-nlp-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-core-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-module-jaxb-annotations.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/objenesis-2.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/picocli-4.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-webarchive-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-sqlite3-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.annotation-api-1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/wsdl4j-1.6.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-beanutils.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/geronimo-stax-api_1.0_spec-1.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/credential-secure-storage-1.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/neko-htmlunit.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/spring-security-crypto-5.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/java-xmlbuilder.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-pool-1.6.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/threaddump.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tagsoup-1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-mail-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.ws.rs-api-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-pool2-2.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jna-5.14.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/picocli-shell-jline3-4.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-entity-filtering-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-dbcp-1.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/log4j-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/certj.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jboss-transaction-api_1.2_spec-1.0.1.Final.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxws-api-2.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/failureaccess.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.xml.soap-api-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-miscoffice-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/dom4j-2.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/namespace.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-coreutils-1.8.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-jdbc-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-code-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/oshi-properties.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/maven-invoker-3.2.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/gson.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bcel-5.1-jnbridge.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-common-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxws-api-jakarta-2.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-dataformat-cbor.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javassist-3.20.0-GA.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-annotations.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/clibwrapper_jiio.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ant-launcher.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.json.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tt-bytecode.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/apache-mime4j-core-0.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/felix.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jandex-2.0.3.Final.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jax-qname-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-lang3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/icu4j-52_2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-compress.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xml-apis.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bcel.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-hk2-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.xml.soap-api-3.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xercesImpl.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-news-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxrpc-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/antisamy.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/imageio-metadata.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/maven-shared-utils-3.3.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpasyncclient-4.1.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-digest-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion-osgi.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xmpcore.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.validation-api-3.0.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-jakarta-rs-base-2.15.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/swagger4j-1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.annotation-api-2.1.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.ws.rs-api-2.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-servlet-core-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/apache-mime4j-dom-0.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/imageio-jpeg.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta-slide-webdavlib-2.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/mlibwrapper_jai.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.inject-2.4.0-b34.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/wsproxyconfig.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/hk2-utils-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cxf-rt-frontend-jaxrs.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-pdf-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/imageio-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jdom2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/antlr-2.7.7.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-dataformat-smile.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-media-json-jackson-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-microsoft-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jose4j.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-crypto-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-scientific-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-cad-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/log4j-api.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jna-platform-5.14.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-dataformat-yaml.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-client-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jnbcore.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/saaj.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jansi.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jcifs-1.3.15.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/guava.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/pdfencryption.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-apple-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-vfs2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpmime-4.5.13.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/hk2-api-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/common-lang.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/gateway/lib/examples.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/gateway/lib: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-ext.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-css.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/commons-discovery.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/commons-logging.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/concurrent.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/jnet.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/jcert.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-transcoder.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-awt-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/bin/cf-osgicli.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis-jakarta-1.2.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/javac-shaded-9+181-r4173-1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis-2025.0.0.331320.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/xmlschema-core-2.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axiom-dom-jakarta-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/woden-core-1.0M10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-kernel-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/commons-fileupload-1.5.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/wsdl4j-1.6.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axiom-api-jakarta-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axiom-impl-jakarta-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-adb-codegen-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-transport-http-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-transport-local-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/google-java-format-1.7.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/geronimo-ws-metadata_2.0_spec-1.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/wstx-asl-3.2.9.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/neethi-3.2.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-codegen-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-jaxws-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-adb-jakarta-1.8.2.jar: &nbsp:                  Java クラスパス            /Applications/ColdFusion2025/cfusion/runtime/bin/tomcat-juli.jar &nbsp: /Applications/ColdFusion2025/cfusion/bin/cf-bootstrap.jar &nbsp: /Applications/ColdFusion2025/cfusion/bin &nbsp                    Java 拡張ディレクトリ                                   プリンタの詳細        デフォルトプリンタ         10.193.64.21        プリンタ      10.193.64.21                      サーバー情報                         一般設定                    リクエストタイムアウト            Yes                    アプリケーションごとの設定の有効化            Yes                    Enable Null Support            No                          Java を正規表現エンジンとして使用            No                      リクエストの時間制限            60 秒                   CFToken 用の UUID の使用            Yes                    空白抑制の有効化            Yes                    サービスファクトリの無効化            No                    シリアル化 JSON の保護            No                    シリアル化 JSON 接頭辞の保護            //                    見つからないテンプレートハンドラ                                サイト全体のエラーハンドラ                                HTTP ステータスコードの有効化            Yes                    グローバルなスクリプト保護            Yes                    ORMSearch インデックスディレクトリ                                デフォルト CFForm ScriptSrc ディレクトリ            /cf_scripts/scripts/                    Google Map キー                                CFInclude タグで許可されるファイル拡張子            *                    送信データの最大サイズ            20 MB                   リクエストのスロットルしきい値            4 MB                   リクエストのスロットルメモリ             200 MB                      エグゼキュータープール設定 (非同期フレームワーク)                    コアプールサイズ            25                    最大プールサイズ            50                     キープアライブ時間            2000 秒                         リクエストの調整                    同時リクエスト制限            25                      Web サービスリクエスト制限            5                    CFC リクエスト制限            15                      CFThread プールサイズ            10                    レポートスレッドの最大数            8                      リクエストキューのタイムアウト            60 秒                   リクエストキューのタイムアウトページ                                      キャッシュ機能                    テンプレートキャッシュサイズ            1024 テンプレート                   信頼できるキャッシュの有効化            No                    キャッシュされたクエリー制限            100                  クラスファイルの保存            Yes                    キャッシュエンジン                Ehcache                        JCS DSN 名                                JCS クラスターが有効か            No                    Memcached サーバー                                Redis サーバー                                Redis ポート            0                    Redis Cluster が有効            No                    カスタムキャッシュ                                        クライアント変数設定                    デフォルトのクライアント変数ストア            Cookie                      破棄の間隔            1 時間 7 分                         クライアントストア                      Cookie                     タイプ            COOKIE                    説明            Client based text file.                        時間制限後データを破棄            Yes                    時間制限            10 日                   グローバルな更新の無効化            No                       Registry                     タイプ            REGISTRY                    説明            System registry.                        時間制限後データを破棄            Yes                    時間制限            90 日                   グローバルな更新の無効化            No                          メモリ変数                      J2EE セッション            No                         アプリケーション変数                   アプリケーション変数の有効化            Yes                    デフォルトのタイムアウト            2,0,0,0                    最大タイムアウト            2,0,0,0                       セッション変数                セッション変数の有効化            Yes                    デフォルトのタイムアウト            0,0,20,0                    最大タイムアウト            2,0,0,0                        ColdFusion マッピング                      /gateway              /Applications/ColdFusion2025/cfusion/gateway/cfc                      /CFIDE              /Applications/ColdFusion2025/cfusion/wwwroot/CFIDE                        メール接続の設定                    デフォルトのサーバーポート            25                    接続タイムアウト            60 秒                   スプール間隔            15 秒                  メール配達スレッド            10                    メールサーバーへの接続を維持            No                      メッセージのスプール先            disk                    メモリにスプールされるメッセージの最大数            50000                    デフォルト CFMail 文字設定            UTF-8                    SSL 接続の使用            No                    TLS の使用            No                     デフォルトのメールサーバー                         メールロギングの設定                        ログの厳格度               warning                         ColdFusion から送信された電子メールメッセージをすべてロギング               No                          チャート                    キャッシュタイプ            disk イメージ                   キャッシュ内のイメージの最大数            50 イメージ                   チャートを処理するスレッドの最大数            1                    ディスクキャッシュの位置            /Applications/ColdFusion2025/cfusion/tmpCache/CFFileServlet/_cf_chart                           データとサービス                         データベースのデータソース                         cfartgallery                     CF データソース名            cfartgallery                    説明                                ドライバ            Apache Derby Embedded                    JDBC URL            **************************************************************************                    ユーザー名                                ログインタイムアウト            30 秒                   長いテキストのバッファーサイズ            64000                    タイムアウト            1200 秒                   接続の維持            Yes                    間隔            420 秒                   制限された SQL オペレーション                                接続の無効化            No                         cfbookclub                     CF データソース名            cfbookclub                    説明                                ドライバ            Apache Derby Embedded                    JDBC URL            ************************************************************************                    ユーザー名                                ログインタイムアウト            30 秒                   長いテキストのバッファーサイズ            64000                    タイムアウト            1200 秒                   接続の維持            Yes                    間隔            420 秒                   制限された SQL オペレーション                                接続の無効化            No                         cfdocexamples                     CF データソース名            cfdocexamples                    説明                                ドライバ            Apache Derby Embedded                    JDBC URL            *****************************************************************************                    ユーザー名                                ログインタイムアウト            30 秒                   長いテキストのバッファーサイズ            64000                    タイムアウト            1200 秒                   接続の維持            Yes                    間隔            420 秒                   制限された SQL オペレーション                                接続の無効化            No                         cfcodeexplorer                     CF データソース名            cfcodeexplorer                    説明                                ドライバ            Apache Derby Embedded                    JDBC URL            ******************************************************************************                    ユーザー名                                ログインタイムアウト            30 秒                   長いテキストのバッファーサイズ            64000                    タイムアウト            1200 秒                   接続の維持            Yes                    間隔            420 秒                   制限された SQL オペレーション                                接続の無効化            No                          Web サービス                        PDF サービスマネージャーのプロパティ                               localhost                          ホスト名               localhost                         ポート               8997                         重み               2                         HTTPS が有効               No                         サービスマネージャーが有効               No                         リモートサービスマネージャー               No                               デバッグとロギング                         デバッグの設定                    デバッグの有効化            No                    Robust 例外情報の有効化            No                    形式の表示            classic.cfm                    実行時間            Yes                    実行時間形式            summary                    実行時間強調表示限度            250 ms                  データベースアクティビティ            Yes                    例外情報            Yes                    トレース情報            Yes                    タイマー情報            No                    変数            Yes                       変数                   アプリケーション            No                    CGI            Yes                    クライアント            Yes                    Cookie            Yes                    フォーム            Yes                    リクエスト            No                    サーバー            No                    セッション            Yes                    URL            Yes                        デバッグする IP アドレス                    デバッグする IP アドレスの制限            127.0.0.1 0:0:0:0:0:0:0:1 ::1                        ラインデバッガの設定                    ラインデバッグの許可            NO                    デバッガポート            5005                    同時デバッグセッションの最大数            5                        ロギングの設定                    ログディレクトリ            /Applications/ColdFusion2025/cfusion/logs                    ファイルの最大サイズ            5000 KB                  アーカイブの最大数            10                      遅いページのロギング            No                    遅いページの時間制限            30 秒                   CORBA 呼び出しのロギング            No                               スケジュールされたタスクのロギング            No                             タスクとプローブのスケジューリング                         スケジュールされたタスク                        システムプローブ                      設定のプローブ                    通知電子メールの受信者                                通知送信者            ColdFusionProbe@localhost                    probe.cfm URL                                probe.cfm ユーザー名                                      システムプローブ                             拡張機能                        CFX タグ                          カスタムタグのパス                      /Applications/ColdFusion2025/cfusion/CustomTags                          CORBA                    選択されたコネクタ                 [なし]                            コネクタ                            イベントゲートウェイ                         設定                    イベントゲートウェイの有効化            No                    スレッドプールサイズ            1                    キューの最大サイズ            10                      ゲートウェイタイプ                       SMS                     説明            Handles SMS text messaging                    クラス            coldfusion.eventgateway.sms.SMSGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       XMPP                     説明            Handles XMPP instant messaging                    クラス            coldfusion.eventgateway.im.XMPPGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       SAMETIME                     説明            Handles Lotus SAMETIME instant messaging                    クラス            coldfusion.eventgateway.im.SAMETIMEGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       DirectoryWatcher                     説明            Watches a directory for file changes                    クラス            examples.watcher.DirectoryWatcherGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       Socket                     説明            Listens on a socket                    クラス            examples.socket.SocketGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       CFML                     説明            Handles asynchronous events through CFCs                    クラス            coldfusion.eventgateway.cfml.CfmlGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       JMS                     説明            Handles Java Messaging Service messages                    クラス            examples.JMS.JMSGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                       ActiveMQ                     説明            Handles Apache ActiveMQ JMS messages                    クラス            examples.ActiveMQ.JMSGateway                    タイムアウト            30 秒                   タイムアウト時にキル            Yes                             セキュリティ                         CF Admin の認証                    ColdFusion Administrator に対する認証の有効化            No                    単一のパスワードによる ColdFusion Administrator へのアクセスの許可            Yes                    管理コンソールの同時ログインセッションを許可            Yes                      RDS の認証                    RDS アクセスに対する認証の有効化            Yes                    単一のパスワードによる RDS 経由のアクセスの許可            Yes                    セキュアプロファイル            No                           セキュリティサンドボックス                         ColdFusion サンドボックスセキュリティを有効化               No                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "reports/index.cfm"}, {"Category": "データとサービス", "SubCategory": "データソース", "Contents": "                                                                                                                                       データとサービス > データソース  データソース接続とデータソース名 (DSN) を追加して、管理してください。 DSN を使用して、ColdFusion をさまざまなデータソースに接続します。           新規のデータソースの追加                              データソース名                                            ドライバ                                有効なドライバタイプを選択 :         Apache Derby Client               Apache Derby Embedded               DB2 Universal Database               J2EE Data Source (JNDI)               Microsoft SQL Server               MySQL               MySQL (DataDirect)               Oracle               その他               PostgreSQL                                                                        接続済みデータソース                                     アクション                      データソース名                      ドライバ              ステータス                                                                                                                                                                              cfartgallery                      Apache Derby Embedded                                                                                                                                                                                                cfbookclub                      Apache Derby Embedded                                                                                                                                                                                                cfcodeexplorer                      Apache Derby Embedded                                                                                                                                                                                                cfdocexamples                      Apache Derby Embedded                                                                                                                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "datasources/index.cfm"}, {"Category": "データとサービス", "SubCategory": "NoSQL データソース", "Contents": "                                                                                                                                      NoSQL データソース接続とデータソース名 (DSN) を追加および管理します。DSN を使用して、ColdFusion を様々なデータソースに接続します。          新規 NoSQL データソースを追加                              データソース名                                            ドライバ                                 有効なドライバータイプを選択 :                          MongoDB                                                                   接続済み NoSQL データソース             アクション           データソース名           ドライバ                 ステータス                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "nosql/index.cfm"}, {"Category": "データとサービス", "SubCategory": "ColdFusion コレクション", "Contents": "                                                                                                                                         データとサービス／ColdFusion コレクション   Solr のインデックス作成エンジンを使用すると、ColdFusion アプリケーション用の検索機能を簡単に開発できます。Solr のコレクションは、セットとしてインデックスを作成して検索できる情報のグループです。Solr コレクションを作成および管理するには、このフォームを使用してください。                          検索サービスからコレクションを取得できません。 ColdFusion 検索サービスがインストールされていて動作していることを確認してください。                                        © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "solr/index.cfm"}, {"Category": "データとサービス", "SubCategory": "Solr サーバー", "Contents": "                                                                                                                                                                              データとサービス／Solr サーバー  ColdFusion が動作しているホスト以外のホストに Solr 検索サービスをインストールして設定することもできます。これは、Coldfusion が検索オペレーションを実行するときに使用するホストです。これらの値を設定するには、「詳細設定を表示」をクリックしてください。ColdFusion がインストールした Solr を実行している場合は、詳細設定の値を変更する必要はありません。         Solr サーバーを設定                Solr ホスト名                                  Solr ホーム                                              詳細設定                       Solr Admin ポート                          Solr Webapp                             Solr サーバーのコンテキストルート。                                                                       Solr バッファー制限                       インデックス作成中にこのサイズ (MB) を超えると、ドキュメントを Solr サーバーにコミットします。バッファー制限を大きくするほど、パフォーマンスが向上します。                                       ユーザー名                             パスワード                                           Solr サーバーで基本認証が有効になっている場合は、資格情報を指定してください。                                                                             Solr 接続                                                   HTTPS 接続を使用                                 Solr Admin HTTPS ポート                                 インデックス作成言語を設定                       ステマーを追加した後、言語と接尾辞を指定します。                       新規言語              新規言語接尾辞                               現在の言語                                     言語名                    言語の接尾辞               アクション                                              Danish                dk                                                                                       Dutch                nl                                                                                       Finnish                fi                                                                                       French                fr                                                                                       German                de                                                                                       Italian                it                                                                                       Norwegian                no                                                                                       Spanish                es                                                                                       Portuguese                pt                                                                                       Russian                ru                                                                                       Swedish                sv                                                                                       Chinese                zh                                                                                       Japanese                ja                                                                                       Korean                ko                                                                                       Czech                cz                                                                                       Greek                el                                                                                       Thai                th                                                                                         コレクションを移行          古い Solr コレクションを移行します。                 古い Solr ホーム                                                                  -->                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "solr/solrserver.cfm"}, {"Category": "データとサービス", "SubCategory": "Web サービス", "Contents": "                                                                                                                                   データとサービス > Web サービス      ColdFusion を使用して Web サービスを登録できます。そのため、Web サービスを呼び出す際に、Web Services Description Language (WSDL) URL 全体を指定する必要はありません。WSDL URL は、初めて参照されるときに自動的に登録されます。         Web サービスのバージョン                                      Web サービスのバージョンを選択                             1             2                                                                     ColdFusion Web サービスの追加 / 編集                             Web サービス名                              WSDL URL                                                                Web サービスに割り当てられた名前                            Web サービスの WSDL の絶対 URL                                                                           認証タイプ                              なし            基本            NTLM                  タイムアウト                                                      Web サービスにアクセスするための認証タイプを選択します                                            Web サービスリクエストのタイムアウト (秒単位)                                ユーザー名                         パスワード                                                                Web サービスにアクセスする際に使用するユーザー名。                                   Web サービスにアクセスする際に使用するパスワード。                                          ドメイン                                           ワークステーション名                               NTLM 認証用のドメイン。                                               NTLM 認証用のワークステーション名。                                                                                                       プロキシサーバー                         プロキシポート                                                   Web サービスの URL にアクセスするために必要なプロキシサーバー                                     プロキシサーバーで使用するポート                                                                  プロキシユーザー名                         プロキシパスワード                                                            プロキシサーバーに送信するユーザー ID                                 プロキシサーバーでのユーザーのパスワード                                                                                                         アクティブな ColdFusion Web サービス                              アクション              Web サービス名              WSDL URL                         Web サービスを使用できません。                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/webservices.cfm"}, {"Category": "データとサービス", "SubCategory": "REST サービス", "Contents": "                                                                                                                                         データとサービス／REST サービス   REST Playground      REST Playground は、Web サービスの開発中に REST サービスの登録、管理、テスト、デバッグをおこなうためのインターフェイスです。 REST Playground を使用するには、 開発者プロファイルの有効化 (デバッグとロギング／開発者プロファイル) オプションを選択します。  .  It is recommended to disable REST Playground in Production/Lockdown environments by   disabling the developer profile  .               アプリケーションとフォルダーを登録してください。登録したフォルダーで検出された CFC が自動的に登録されます。         REST サービスを追加 / 編集                                 ルートパス                                                             CFC が保存されているアプリケーションパスまたはルートフォルダー                        ホスト                                                 REST サービスのホスト名。例 : localhost:8500 (オプション)                        サービスマッピング                                             REST サービスの呼び出し時にアプリケーション名で使用される代替文字列。 (オプション) 例 : http://{ホスト名}:{ポート}/{REST パス}/{サービスマッピング}/{コンポーネント REST パス}                      デフォルトアプリケーションとして設定する                                                       Web サービスの呼び出し中に URL のアプリケーション名を除外するには、アプリケーションをデフォルトに設定します。ホストで許可されるデフォルトアプリケーションは 1 つです。  例 : http://localhost/rest/path                                                                            アクティブな ColdFusion REST サービス                              アクション              ルートパス              サービスマッピング              デフォルト              ホスト                         REST サービスを使用できません。                                                       REST パスを更新                                                URL をカスタマイズするには、この設定を変更します。例えば、この設定を「comservices」に変更する場合、URL は http://{ホスト名}:{ポート}/comservices/{サービスマッピング}/{リソース REST パス} のようになります。                                                                                    © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/restwebservices.cfm"}, {"Category": "データとサービス", "SubCategory": "PDF サービス", "Contents": "                                                                                                                                   データとサービス > PDF サービス     ColdFusion を使用すると、複数の PDF サービスマネージャーを登録できます。これらの PDF サービスマネージャーは、CFHtmlToPdf タグに対する PDF 変換リクエストを処理します。      PDF サービスマネージャーを追加 / 編集                                名前                                      PDF サービスマネージャーの一意の名前。                     ホスト名                        PDF サービスマネージャーのホスト名。                      ポート                        PDF サービスマネージャーのポート。                               重み                        PDF サービスマネージャーの重み。                     HTTPS が有効                               PDF サービスマネージャーが HTTPS で実行されている場合。                             エンジン                          1.0          2.0                        サービスマネージャーのエンジン                                                         PDF サービスマネージャー                                     アクション               名前               ホスト名               ポート               重み               HTTPS が有効               エンジン               接続ステータス                                                                                                                                                                                                                                                     localhost                      127.0.0.1                     8997                     2                     NO                     2.0                                                                                                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "document/pdfgservice.cfm"}, {"Category": "データとサービス", "SubCategory": "クラウド資格情報", "Contents": "                                                                                                                                     クラウド資格情報  ColdFusion を使用すると、クラウドサービス資格情報を追加および管理できます。    クラウド資格情報を追加 / 編集                                                 資格情報エイリアス                                                                                                                          クラウドベンダー                                                                 AWS                      AZURE                      GCP                                                                                                AWS リージョン                                                                                                                          AWS アクセスキー                                                                                                                          AWS シークレットキー                                                                                                                                                                          AWS Session Token                                                                                                                                                 Azure 接続文字列                                                                                                         GCP プロジェクト ID                                                                                                                          Credential JSON Filepath                                                                     例 : EndPoint=sb://(namespace).servicebus.windows.net/;SharedAccessKeyName=(key);SharedAccessKey=(key)                                                          格納された資格情報             アクション           クラウドプロファイル名           クラウドベンダー                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "cloudservices/profiles.cfm"}, {"Category": "データとサービス", "SubCategory": "クラウド設定", "Contents": "                                                                                                                                     クラウドサービス設定を追加 / 編集    ColdFusion を使用すると、クラウドサービスの設定を追加できます。           クラウドサービス設定を追加 / 編集                                                                                                                  エイリアスの設定                                                                                                                                                                                                                           クラウドベンダー                                                                                                 AWS                              AZURE                              GCP                                                                                                                                                                                 サービス名                                                                                                 S3                              SQS                              SNS                              DynamoDB                                                                                BLOB                               Service Bus                                                                                Firestore                              ストレージ                              PubSub                                                                                                                                                                                                                                                          格納されたサービス設定             アクション           名前           クラウドサービス                 クラウドベンダー                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "cloudservices/services.cfm"}, {"Category": "データとサービス", "SubCategory": "GraphQL", "Contents": "                                                                                                                                    GraphQL 設定                 GraphQL サービス設定             GraphQL クライアント設定を追加および管理                                                                                                                                                                                GraphQL クライアント設定             GraphQL サービス設定を追加および管理                                                                                                                   設定のエイリアス                                                                                                                                                                                                                           サービス名                                                                                                  オプションの選択                                                   myservice                                                                                                                                                                                                                                                                                                                                  GraphQL サービス設定を定義しました             アクション           サービス名                 エンドポイント URL / スキーマパス                                                                                                                               myservice                                           https://apollo-fullstack-tutorial.herokuapp.com/graphql                               GraphQLクライアント設定を定義しました             アクション           エイリアス                 サービス名                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "graphql/index.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "デバッグ出力の設定", "Contents": "                                                                                                                                                                     デバッグとロギング > デバッグ出力の設定             Robust 例外情報の有効化          例外ページで、訪問者は次の情報を見ることができます。  テンプレートの物理パス  テンプレートの URI  行番号と行スニペット  使用される SQL ステートメント (ある場合)  データソース名 (ある場合)  Java スタックトレース                AJAX デバッグログウィンドウの有効化         URL で cfdebug フラグが渡された場合に AJAX デバッグログウィンドウを表示します。このオプションを無効にすると、cfdebug フラグが指定されている場合でも AJAX デバッグログウィンドウは表示されません。              リクエストのデバッグ出力の有効化         CFML ページに対するページレベルのデバッグ出力を有効にします。以下の設定をすべて上書きするには、このチェックボックスをオフにします。デバッグ情報は各リクエストの最後に追加されます。                       デバッグ出力をカスタマイズ            デバッグ出力形式の選択      dockable.cfm     classic.cfm    ColdFusion にはいくつかのデバッグ出力形式があります。  classic.cfm  - この形式は ColdFusion 5 とそれ以前のバージョンで使用可能です。この形式では基本的な表示が出力され、ブラウザの制限はほとんどありません。  dockable.cfm  - どこにでも配置できるツリーベースのデバッグパネル。パネルとブラウザの制限については、オンラインヘルプを参照してください。             実行時間のレポート    次の時間を超過したテンプレートを強調表示     (ミリ秒) し、使用する出力モードは   summary  tree   テンプレート、インクルード、モジュール、カスタムタグ、コンポーネントメソッド呼び出し用の実行時間。この最小強調表示時間を超えたテンプレート実行時間は、赤で表示されます。デフォルトは 250 ミリ秒です。ColdFusion には次のテンプレートモードがあります。  summary  - 呼び出された各ページの要約。合計時間、平均時間、カウント、テンプレートがカラムに含まれます。合計時間の大きい順に並び替えられます。  tree  - ページ実行ごとの階層ツリー表示。メモ : 処理時間と出力は summary より長くなります。                  一般デバッグ情報         このリクエストについての一般情報を表示する場合は、このオプションを選択してください。一般項目は、ColdFusion のサーバーバージョン、テンプレート、タイムスタンプ、ユーザーロケール、ユーザーエージェント、ユーザー IP、ホスト名です。              データベースアクティビティ         SQL クエリーイベントとストアドプロシージャイベント用のデータベースアクティビティをデバッグ出力に表示する場合は、このオプションを選択します。              例外情報         リクエストで発生した ColdFusion 例外をデバッグ出力に記録する場合は、このオプションを選択します。                トレース情報         トレースイベント情報をデバッグ出力に表示する場合は、このオプションを選択します。トレース操作により、開発者は CFTRACE タグを使用して、プログラムフローと効率を追跡できます。              タイマー情報         タイマーイベント情報をデバッグ出力に表示する場合は、このオプションを選択します。タイマーにより、開発者は CFTIMER タグの開始タグから終了タグまでのコードの実行時間を追跡できます。              Flash フォームコンパイルエラーとメッセージ         (開発用のみ) このオプションを選択すると、ColdFusion は Flash フォームのコンパイル時に ActionScript エラーをブラウザーに表示するようになり、ページの表示時間に影響します。             変数   変数レポートを有効にする場合は、このオプションを選択します。以下の変数を選択してください。               アプリケーション               Cookie               サーバー                 CGI               フォーム               セッション                 クライアント               リクエスト               URL                                  メトリクスのロギングを有効にする               ColdFusion のメトリクスのロギングを有効にするには、このオプションを選択します。                       メトリクスの頻度                  ColdFusion のメトリクスは、この頻度でログに記録されます。                             CFSTAT 設定                                           CFSTAT の有効化            cfstat コマンドラインユーティリティは、ColdFusion のパフォーマンス測定値をリアルタイムで提供します。cfstat は測定値データを得るためにソケット接続を使用し、システムモニターアプリケーションを実際に使用することなく、ColdFusion からシステムモニターに書き込まれた情報を表示します。                                   CFSTAT ホスト                                               CFSTAT サーバーのバインド先となる IP アドレスまたはホスト名。デフォルト値は 127.0.0.1 です。                               コネクタポート              ColdFusion の cfstat コマンドラインユーティリティのパフォーマンス測定値は、このポートのパフォーマンス測定値を読み取ります。                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "debugging/index.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "開発者プロファイル", "Contents": "                                                                                                                               デバッグとロギング > デバッグ出力の設定     開発者プロファイルの有効化    開発者プロファイルを有効にすると、次の設定が変更されます。  信頼できるキャッシュが無効になります  Robust 例外が有効になります  REST 確認が有効になります    注意 : 本番用の開発者プロファイルを無効にする際に、API Manager の REST 確認を許可する場合は、サーバーの設定／設定にある「REST 確認」設定を有効にしてください。  開発者プロファイルを有効にすると、次の設定が変更されます。  信頼できるキャッシュが無効になります  Robust 例外が有効になります  REST 確認が有効になります    注意 : 本番用の開発者プロファイルを無効にする際に、API Manager の REST 確認を許可する場合は、サーバーの設定／設定にある「REST 確認」設定を有効にしてください。                                                                © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "debugging/devprofile.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "デバッグする IP アドレス", "Contents": "                                                                                                                               デバッグとロギング > デバッグする IP アドレス   AJAX デバッグログウィンドウのメッセージを含むデバッグメッセージを受け取る IP アドレスを指定してください。リスト内に IP アドレスを追加する場合は、アドレスを入力して [追加] をクリックしてください。リストから IP アドレスを削除する場合は、アドレスを選択して [選択の削除] をクリックしてください。IP アドレスが選択されない場合は、すべてのユーザーがデバッグ情報を受け取ります。         デバッグ出力用の IP アドレスの選択                            IP アドレス                                                デバッグ出力用に選択した IP アドレスの表示 / 削除                                        127.0.0.1             0:0:0:0:0:0:0:1             ::1                                                                    © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "debugging/iplist.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "デバッガの設定", "Contents": "                                                                                                                               デバッグとロギング > デバッガの設定   Eclipse で動作する ColdFusion デバッガ (ColdFusion Builder) を使用するには、「ラインデバッグの許可」オプションを有効にします。ポートおよび同時デバッグセッションの最大数を指定します。           ラインデバッガの設定                                           ラインデバッグの許可                              デバッガポート :                                                                 同時デバッグセッションの最大数 :                                                                                                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "debugging/linedebugger.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "ロギングの設定", "Contents": "                                                                                                                            デバッグとロギング > ロギングの設定     ログディレクトリ        エラーログファイルが書き込まれるディレクトリを入力するか、[サーバーのブラウズ] をクリックして、ディレクトリツリーから選択してください。指定するドライブには、十分なディスク容量と、ColdFusion システムサービスに対するセキュリティ権限が必要です。この変更を有効にするために、ColdFusion サービスをシャットダウンして再起動する必要があります。            最大ファイルサイズ (KB)            ログファイルの最大ファイルサイズを入力してください。このサイズに達すると、ファイルは自動的にアーカイブされます。この変更を有効にするには、ColdFusion サービスをシャットダウンして再起動する必要があります。             アーカイブの最大数            作成されるログのアーカイブの最大数を設定します。最大数に達すると、ファイルは古い順に削除されます。              処理時間の遅いページのロギング    秒        サイトにおける潜在的問題やボトルネックを診断するために、ColdFusion では、指定した時間を過ぎて返されるページの名前をロギングできます。これを有効にすると、すべての出力が server.log ファイルに書き込まれます。                   すべての CORBA 呼び出しをロギング         すべての CORBA 呼び出しがロギングされ、設定の問題の診断に役立ちます。これを有効にすると、すべての出力が server.log ファイルに書き込まれます。              スケジュールされたタスクのロギングを有効化         ColdFusion Executive タスクのスケジューリングをロギングします。                                                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "logging/settings.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "ログファイル", "Contents": "                                                                                                                                 デバッグとロギング > ログファイル    ColdFusion により、アプリケーションのトラブルシューティングやイベントの追跡に役立つログファイルがいくつか作成されます。このページを使用して、ログファイルの検索、表示、ダウンロード、アーカイブ、削除ができます。           使用可能なログファイル                                         アクション               ファイル名                 タイプ                サイズ                最終更新日                                                                                                                                   application.log                      CFML                    154               Jan 29, 2025 6:57:49 PM                                                                                                                                    audit.log                      CFML                    9258               Jan 29, 2025 7:07:06 PM                                                                                                                        axis2.log                      Other                    480               Jan 29, 2025 7:01:08 PM                                                                                                                                                    cfpm-audit.log                      CFML                    206631               Jan 29, 2025 7:01:06 PM                                                                                                                        coldfusion-error.log                      Other                    10126               Jan 29, 2025 7:01:08 PM                                                                                                                        coldfusion-out.log                      Other                    25346               Jan 29, 2025 7:03:57 PM                                                                                                                                                    eventgateway.log                      CFML                    266               Jan 29, 2025 7:01:06 PM                                                                                                                                                    http.log                      CFML                    1168               Jan 29, 2025 7:03:57 PM                                                                                                                                                    monitor.log                      CFML                    228               Jan 29, 2025 6:58:16 PM                                                                                                                                    server.log                      CFML                    15698               Jan 29, 2025 7:01:07 PM                                                                                                                                        websocket.log                      Other                    0               Jan 29, 2025 11:21:08 AM                                                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "logging/index.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "システムプローブ", "Contents": "                                                                                                                                   デバッグとロギング／システムプローブ    システムプローブは、規則的な間隔で URL のコンテンツを調べることにより、Web アプリケーションの状態を監視できます。コンテンツが予期されたものではなかった場合、プローブは失敗を通知する電子メールを送信するか、スクリプトを実行できます。            システムプローブ                            アクション              プローブ名              ステータス              間隔              URL                     プローブが定義されていません。                                     通知電子メールの受信者                                       電子メール                                       probe.cfm URL                                       probe.cfm ユーザー名                                       probe.cfm パスワード                                                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "scheduler/probes.cfm"}, {"Category": "デバッグとロギング", "SubCategory": "コードアナライザ", "Contents": "                                                                                                                            デバッグとロギング > コードアナライザ   コード互換性アナライザを利用して、以前のバージョンの ColdFusion からこのバージョンの ColdFusion に移行できます。このコード互換性アナライザは、指定された CFML ページを調べ、重大な互換性の問題があれば、それを報告します。サポートされない非推奨の CFML 機能を検出し、スムーズな移行を確実にするために必要な実装の変更を概略します。            コード互換性アナライザ             分析するディレクトリ                                          サブディレクトリの分析        ファイルタイプを分析                CFM、CFC      CFM      CFC                                CFML の検証        テストするコードのバージョン               CF2023      CF2021      CF2018                                                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "analyzer/index.cfm"}, {"Category": "Performance Monitoring Toolset", "SubCategory": "監視設定", "Contents": "                                                                                                                                 PMS              ColdFusion ホスト名        ColdFusion インスタンスの IP アドレスまたは DNS 名を入力     共有シークレットを監視中               シークレットを表示  -   087b8c16-e016-4652-84dc-fa2c88314724    ColdFusion を使用して Performance Monitoring Toolset を設定するシークレット      データストアに接続されています       監視有効                                                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "pms/index.cfm"}, {"Category": "拡張機能", "SubCategory": "CFX タグ", "Contents": "                                                                                                                               拡張機能 > CFX タグ  CFX タグは、CFML を拡張して強化するために ColdFusion アプリケーションプログラミングインターフェイス (API) に対して書かれたカスタムタグです。Windows プラットフォーム上では DLL として、UNIX プラットフォーム上では共有オブジェクト (.so/sl) として C++ を使用して、CFX タグを構築します。cfx.jar ファイル内で定義されている Java インターフェイスを拡張することにより、Java CFX は構築されます。                                                                                                     登録された CFX タグ               アクション      タグ名      タイプ      説明           CFX タグが見つかりません。                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/cfx.cfm"}, {"Category": "拡張機能", "SubCategory": "カスタムタグのパス", "Contents": "                                                                                                                                   拡張機能 > カスタムタグのパス  カスタムタグによって、ColdFusion Markup Language (CFML) の機能が拡張されます。デフォルトのカスタムタグのパスは、インストールディレクトリ内にあります。ここでカスタムタグに他のパスを指定することもできます。         新規カスタムタグのパスの登録              新規パス                                             現在のカスタムタグのパス             アクション      パス                                                                                                     /Applications/ColdFusion2025/cfusion/CustomTags                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/customtagpaths.cfm"}, {"Category": "拡張機能", "SubCategory": "CORBA コネクタ", "Contents": "                                                                                                                               ColdFusion no longer supports corba.                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "extensions/corba.cfm"}, {"Category": "イベントゲートウェイ", "SubCategory": "設定", "Contents": "                                                                                                                              イベントゲートウェイ > 設定                                                      ColdFusion イベントゲートウェイサービスの有効化         ColdFusion イベントゲートウェイサービスを有効にします。このサービスにより、外部ソースからのイベントを指定の ColdFusion コンポーネントに渡すことができます。この設定を変更すると、サービスが直ちに起動または停止されます。             イベントゲートウェイ処理スレッド          イベントが到着したときに ColdFusion 関数を実行するために使用されるスレッドの最大数を指定します。数値が高いほどより多くのリソースが使用されますが、イベントのスループットが増加します。スタンダード版またはデベロッパー版では、この値が 1 を超えてはなりません。             キューに入れるイベントの最大数          イベントキューで使用できるイベントの最大数を指定します。キューの長さがこの値を超えると、ゲートウェイのイベントは処理キューに追加されません。これはすべてのイベントゲートウェイ用のグローバルな設定です。デベロッパー版では、この値が 10 を超えてはなりません。       SMS テストサーバー   SMS ゲートウェイアプリケーションのテストを補助するために、ColdFusion には SMS テストサーバーが内蔵されています。これは、あらかじめ設定済みの SMS テストゲートウェイと共同で機能します。SMS テストサーバーを起動した後、SMS テストゲートウェイを有効にし、SMS テストクライアントを使用して、アプリケーションをテストできます。 テストサーバーは現在停止しています。                                             © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "eventgateway/index.cfm"}, {"Category": "イベントゲートウェイ", "SubCategory": "ゲートウェイタイプ", "Contents": "                                                                                                                                        イベントゲートウェイ > ゲートウェイタイプ  システムで使用できるゲートウェイのタイプを設定します。タイプの設定後、そのタイプのゲートウェイインスタンスを任意の数だけ作成できます。         ColdFusion イベントゲートウェイタイプの追加 / 編集                              タイプ名                                         説明                                           Java クラス                                    起動タイムアウト (秒)                                                           スタートアップタイムアウト時に停止                                                           設定済み ColdFusion ゲートウェイタイプ                             アクション              名前              説明              Java クラス              タイムアウト              タイムアウト時にキル                                                                                                                                                                    ActiveMQ                               Apache ActiveMQ JMS メッセージを処理する                   examples.ActiveMQ.JMSGateway                  30                  YES                                                                                                                                                                        CFML                               CFC を使用して非同期イベントを処理する                   coldfusion.eventgateway.cfml.CfmlGateway                  30                  YES                                                                                                                                                                        DirectoryWatcher                               ディレクトリでのファイルの変更を監視する                   examples.watcher.DirectoryWatcherGateway                  30                  YES                                                                                                                                                                        JMS                               Java Messaging Service メッセージを処理する                   examples.JMS.JMSGateway                  30                  YES                                                                                                                                                                        SAMETIME                               Lotus SAMETIME インスタントメッセージを処理する                   coldfusion.eventgateway.im.SAMETIMEGateway                  30                  YES                                                                                                                                                                        SMS                               SMS テキストメッセージングを処理する                   coldfusion.eventgateway.sms.SMSGateway                  30                  YES                                                                                                                                                                        Socket                               ソケットをリスンする                   examples.socket.SocketGateway                  30                  YES                                                                                                                                                                        XMPP                               XMPP インスタントメッセージを処理する                   coldfusion.eventgateway.im.XMPPGateway                  30                  YES                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "eventgateway/gatewaytypes.cfm"}, {"Category": "イベントゲートウェイ", "SubCategory": "ゲートウェイインスタンス", "Contents": "                                                                                                                                        イベントゲートウェイ > ゲートウェイインスタンス  ColdFusion イベントゲートウェイインスタンスを設定して、様々なソースから既に書かれた ColdFusion コンポーネントにイベントを誘導できます。         ColdFusion ゲートウェイインスタンスの追加 / 編集                            ゲートウェイ ID                                            ゲートウェイタイプ                    タイプを選択してください              ActiveMQ - Handles Apache ActiveMQ JMS messages               CFML - Handles asynchronous events through CFCs               DirectoryWatcher - Watches a directory for file changes               J<PERSON> - Handles Java Messaging Service messages               SAMETIME - Handles Lotus SAMETIME instant messaging               SMS - Handles SMS text messaging               Socket - Listens on a socket               XMPP - Handles XMPP instant messaging                                               CFC パス                                                     設定ファイル                                                     スタートアップモード                     自動        手動        無効                                                                    設定済み ColdFusion イベントゲートウェイインスタンス                            アクション              ステータス              ゲートウェイ ID              タイプ              スタートアップ              イン              アウト              CFC パス              ゲートウェイ設定                                                                                                                                                                                                                                                  停止済み                       SMS Menu App - 5551212                 SMS                手動                0               0               {cf.rootdir}/gateway/cfc/examples/menu/main.cfc                {cf.rootdir}/gateway/config/sms-test.cfg                                                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "eventgateway/gateways.cfm"}, {"Category": "セキュリティ", "SubCategory": "Administrator", "Contents": "                                                                                                                                          セキュリティ／Administrator    ColdFusion Administrator の認証        ColdFusion Administrator へのアクセスは、信頼できるユーザーに限定してください。デフォルトでは、これらのページにアクセスするために認証が必要です。ただし、これらのページへのアクセスを制限するように Web サーバーを設定する場合は、この認証を無効にして、代わりに Web サーバーのセキュリティを利用することもできます。(ページのセキュリティ設定の詳細については、Web サーバーのマニュアルを参照。)      Administrator の認証タイプを次の中から選択してください。          単一のパスワードのみを使用する (デフォルト)           別のユーザー名とパスワードで認証する (複数のユーザーを許可する)         認証を必要としない (推奨しません)              ルート Administrator パスワード         ルート管理ユーザーの ColdFusion Administrator パスワードを変更するには、古いパスワードと新規のパスワードを入力し、確認してください        古いパスワード          新規のパスワード          パスワードの確認      (50 文字まで)           パスワードシード         データソースのパスワードを暗号化するための新しいシード値を指定するには    新規シード    (8 文字以上、500 文字以下。)         同時ログインセッション             Administrator コンソールの同時ログインセッションを許可        External Authentication               なし                              SAML             ID プロバイダー :            --オプションの選択--               サービスプロバイダー :            --オプションの選択--               SAML groupName エイリアス :   i 追加したグループのユーザーがそのグループの権限でログインできるようになります。                       LDAP          LDAP 設定を追加    LDAP 設定を編集    接続を確認    接続が確認されました      接続を確認できません                                                                        © 1995 - 2025 Adobe. All Rights Reserved.                                                                                                                LDAP 詳細                        接続                                        ホスト* i Ldap サーバーのホスト名または IP アドレス                                                      TCP ポート* i Ldap サーバー通信用ポート番号                                                                                 ユーザーバインド DN i 提供されたバインド DN を使用してサーバーに接続。例 : ou=admin、ou=adobe、c=com。匿名で接続するには空のままにします。                                                      ユーザーバインドパスワード i 提供されたパスワードを使用してディレクトリに接続                                                                                              ユーザー BaseContext* i ユーザー検索用 Ldap ツリーの開始ポイント。例 : ou=users、dc=adobe、dc=com                                                                                         グループ BaseContext* i グループ検索用 Ldap ツリーの開始ポイント。例 : ou=groups、dc=adobe、dc=com                                                                           サーバータイムアウト* i Ldap サーバー検索のタイムリミット (ミリ秒)                                                                     SSL/TLS i 接続を保護します。サーバーは、指定されたポートを使用して ssl 接続する必要があります。JVM が使用するトラストストアには、必要な証明書がインストールされている必要があります。                                     TLS を開始 i 単一の接続で Ldap サーバーに対して安全で平文のリクエストを送信します。サーバーはクリアテキストの Ldap 接続のために指定されたポートを使用する必要があります。                                                       Configuration                            ユーザー設定* i ユーザーのオブジェクトクラス                             追加                                                          ユーザー名属性* i アカウントのユーザー名を保持する属性                                                                                 グループ設定* i グループのオブジェクトクラス                                追加                                                           グループ名属性* i グループ名を保持する属性                                                          保存       閉じる                ", "Link": "security/cfadminpassword.cfm"}, {"Category": "セキュリティ", "SubCategory": "RDS", "Contents": "                                                                                                                                   セキュリティ／RDS                RDS サービスを有効にする              ColdFusion RDS サービスを利用すると、以下で定義する RDS パスワードを使用してこのサーバーに接続できます。これは、開発での使用のみを意図としています。本番マシンの場合は、このオプションのチェックを解除したままにしてください。          RDS の認証     RDS へのアクセスは、信頼できるユーザーに限定してください。デフォルトでは、RDS には認証が必要です。ただし、この認証を無効にして、Web サーバーのセキュリティを利用することもできます。     RDS の認証タイプを次の中から選択してください。          単一のパスワードのみを使用する (デフォルト)            別のユーザー名とパスワードで認証する (複数のユーザーを許可する)          認証を必要としない (推奨しません)                   単一の RDS パスワード       単一の RDS パスワードを変更するには、古いパスワードと新規のパスワードを入力し、確認してください           古いパスワード             新規のパスワード             パスワードの確認            (50 文字まで)                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                     ", "Link": "security/cfrdspassword.cfm"}, {"Category": "セキュリティ", "SubCategory": "サンドボックスセキュリティ", "Contents": "                                                                                                                                       セキュリティ > サンドボックスセキュリティ                                                     ColdFusion サンドボックスセキュリティの有効化       ColdFusion のサンドボックスセキュリティでは、ColdFusion リソースへのアクセスを制御するために、ColdFusion ページの位置が使用されます。サンドボックスは、セキュリティ制限を適用するサイトの指定領域 (ファイルやディレクトリ) です。デフォルトで、サブディレクトリ (または子のディレクトリ) は、1 レベル上のディレクトリ (親ディレクトリ) のサンドボックス設定を継承します。サブディレクトリ用のサンドボックス設定を定義すると、親ディレクトリから継承されたサンドボックス設定は上書きされます。   メモ :  この設定は、サーバーで有効化する前に行ってください。また、この設定を有効にするために、ColdFusion アプリケーションサーバーを再起動する必要があります。                    セキュリティサンドボックスの追加                                                                             サンドボックスの新規作成、またはコピーするサンドボックスの選択                                                                                       定義されたディレクトリ権限                                         アクション                 ディレクトリ                                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                     ", "Link": "security/index.cfm"}, {"Category": "セキュリティ", "SubCategory": "ユーザーマネージャ", "Contents": "                                                                                                                                   セキュリティ > ユーザーマネージャ    ユーザーを追加および管理します。ロールとサンドボックスへのアクセス権をユーザーに付与できます。                                                     定義済みのユーザー                Action           ユーザー           ユーザータイプ           Description           RDS           管理アクセス                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                     ", "Link": "security/usermanager.cfm"}, {"Category": "セキュリティ", "SubCategory": "使用できる IP アドレス", "Contents": "                                                                                                                              セキュリティ > 使用できる IP アドレス   ColdFusion Administrator および ColdFusion Internal Directories にアクセスできるクライアント IP アドレスを指定します。個別の IP アドレス、10-30 の形式の IP アドレス範囲、ワイルドカード * を指定できます。IPv4 と IPv6 の両方のアドレスがサポートされます。IP アドレスをリストに含めるには、アドレスを入力して「追加」をクリックします。IP アドレスをリストから削除するには、アドレスを選択して「選択の削除」をクリックします。IP アドレスを選択しないと、すべてのユーザーがアクセスを許可されます。        ColdFusion Administrator および ColdFusion Internal Directories にアクセスするために使用できる IP アドレス                                            IP アドレス                                                                            127.0.0.1             ::1                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                    ", "Link": "security/allowedipaddress.cfm"}, {"Category": "セキュリティ", "SubCategory": "セキュアプロファイル", "Contents": "                                                                                                                              セキュリティ > セキュアプロファイル                                             セキュアプロファイルを有効にする    セキュアプロファイル設定は 1 つの推奨事項にすぎません。要件に応じて、サーバーをさらに詳細に設定する必要があります。この影響を受ける設定を次の表に示します。      セキュアプロファイル設定の要約        設定名   現在の値   セキュアのデフォルト値        Robust 例外情報の有効化             false           false            RDS 用の別の UserID が無効             true           false            Flash Policy Server を起動             true           false            見つからないテンプレートエラーハンドラー                         /CFIDE/administrator/templates/missing_template_error.cfm            Admin 用に別の UserID が必要             false           true            RDS 認証が有効             true           true            ColdFusion Java 内部コンポーネントへのアクセスの無効化             false           true            RDS の有効化             true           true            送信データの最大サイズ (MB)             20.0           20.0            リクエストキューのタイムアウトページ                         /CFIDE/administrator/templates/request_timeout_error.htm            セッション Cookie のタイムアウト (分)             15768000           1440            Admin の認証が有効             false           true            cftoken 用の UUID の使用             true           true            Administrator コンソールの同時ログインセッションを許可             true           false            ColdFusion の内部 Cookie の更新を無効化             false           true            WebSocket サーバーを有効化             true           false            CFInclude タグで許可されるファイル拡張子             *           CFM,CFML            CFSTAT の有効化             true           false            グローバルなスクリプト保護             true           true                使用できる IP アドレス             127.0.0.1,::1           127.0.0.1,::1            サイトエラーハンドラー                         /CFIDE/administrator/templates/secure_profile_error.cfm            DSN 用のストアドプロシージャの作成、削除、変更、付与、破棄を無効化           drop,storedproc,revoke,create,alter,grant : true,true,true,true,true,true     drop,storedproc,revoke,create,alter,grant : false,false,false,false,false,false                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                    ", "Link": "security/secureprofile.cfm"}, {"Category": "セキュリティ", "SubCategory": "IDP 構成", "Contents": "                                                                                                                                    IDP 設定      SAML ID プロバイダーを追加および管理します。                                                    定義された ID プロバイダー                アクション           名前           説明                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/idpconfig.cfm"}, {"Category": "セキュリティ", "SubCategory": "SP 構成", "Contents": "                                                                                                                                    SP 設定         SAML サービスプロバイダーを追加および管理                                                                                                                                                                                                                                                                                                                                     定義されたサービスプロバイダー             アクション           名前           説明                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/spconfig.cfm"}, {"Category": "パッケージとデプロイ", "SubCategory": "ColdFusion アーカイブ", "Contents": "                                                                                                                                          パッケージとデプロイ > ColdFusion アーカイブ    適切な CAR ファイルへのパスを入力するかブラウズして、ファイルをこのサーバーへデプロイし、関連のあるサーバー設定を更新してください。                 既存のアーカイブのデプロイ                                                                                                          ColdFusion では、作業の整理、ファイルのアーカイブ、サイトの移行とデプロイなどを行うためのアプリケーションを定義できます。ColdFusion アーカイブ定義を作成してストアし、後日、アプリケーションのアーカイブ、移行、リデプロイなどを行うことができます。                                           アーカイブを作成                                                  アーカイブ名                                                                            現在のアーカイブ定義リスト                                         アクション                 アーカイブ名                              アーカイブは定義されていません                                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "archives/index.cfm"}, {"Category": "パッケージとデプロイ", "SubCategory": "JEE アーカイブ", "Contents": "                                                                                                                             パッケージとデプロイ > JEE アーカイブ        JEE アーカイブ (EAR または WAR) ファイルを作成します。           新規アーカイブの追加                             アーカイブ名                                                           設定済みアーカイブ                             アクション              アーカイブ名              タイプ              最終ビルト                        アーカイブは定義されていません                                               © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "j2eepackaging/index.cfm"}, {"Category": "エンタープライズマネージャ", "SubCategory": "インスタンスマネージャ", "Contents": "                                                                                                                                 エンタープライズマネージャー／インスタンスマネージャー    ColdFusion エンタープライズ版では、新しいサーバーインスタンスを作成して管理できます。ローカルに (このマシンに) 作成して実行することも、        リモートサーバーを作成してクラスタに登録することもできます。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               使用可能なサーバー                                                       クラスタによるフィルタ                                                                                  >                                                                                                                                      アクション            名前            サーバーディレクトリ            HTTP ポート            リモートポート            ホスト            クラスタ                                                                                                                                                                                                                                                             cfusion              /Applications/ColdFusion2025/cfusion               8500             8024             localhost                      なし                                                             © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "entman/index.cfm"}, {"Category": "エンタープライズマネージャ", "SubCategory": "クラスタマネージャ", "Contents": "                                                                                                                                エンタープライズマネージャー／クラスタマネージャー                    新規クラスタの追加                             クラスタ名                                                                       設定済みクラスタ                           アクション            クラスタ名            クラスタ内のサーバー                  クラスタが定義されていません。                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "entman/cluster.cfm"}, {"Category": "パッケージマネージャー", "SubCategory": "パッケージ", "Contents": "                                                                                                                                             サーバー更新／更新                       クリックすると、import コマンドを使用して他の ColdFusion インスタンスに適用できるすべてのインストール済みパッケージのリストが書き出されます。                                                            コアサーバー                                                                                                                                                     コアパッケージに対するアップデートはありません。                                                                                                                                                  名前                                                                              アップデートレベル :                                                                              アップデートのタイプ :                                                                              Build Number:                                                                              アップデートの説明 :                                                                              Technote Link                                                                               インストール日 :                                                                              バックアップディレクトリ :                                                                              アンインストーラーの場所 :                                                                              インストールログディレクトリ :                                                                              使用可能なバージョン                                                                                                                        このアップデートの以前のインストールでエラーが発生しました。フォルダー内のログを参照してください    and fix the root cause before re-applying the hotfix again.                                                                          Starting Download...                                                                                                                                                                                                                                                                                                     インストール済みのパッケージ                           これらのパッケージはシステムに既にインストールされています。                                                すべてアンインストール                                                                                                                                                                                                                                                                                                                                                           adminapi                                                                                                                                                                                                                                                            administrator                                                                                                                                                                                                                                                            ajax                                                                                                                                                                                                                                                            awsdynamodb                                                                                                                                                                                                                                                            awslambda                                                                                                                                                                                                                                                            awss3                                                                                                                                                                                                                                                            awss3legacy                                                                                                                                                                                                                                                            awssns                                                                                                                                                                                                                                                            awssqs                                                                                                                                                                                                                                                            axis                                                                                                                                                                                                                                                            azureblob                                                                                                                                                                                                                                                            azureservicebus                                                                                                                                                                                                                                                            caching                                                                                                                                                                                                                                                            ccs                                                                                                                                                                                                                                                            cfmongodb                                                                                                                                                                                                                                                            chart                                                                                                                                                                                                                                                            db2                                                                                                                                                                                                                                                            debugger                                                                                                                                                                                                                                                            derby                                                                                                                                                                                                                                                            document                                                                                                                                                                                                                                                            dotnet                                                                                                                                                                                                                                                            eventgateways                                                                                                                                                                                                                                                            exchange                                                                                                                                                                                                                                                            feed                                                                                                                                                                                                                                                            ftp                                                                                                                                                                                                                                                            gcpfirestore                                                                                                                                                                                                                                                            gcppubsub                                                                                                                                                                                                                                                            gcpstorage                                                                                                                                                                                                                                                            graphqlclient                                                                                                                                                                                                                                                            htmltopdf                                                                                                                                                                                                                                                            image                                                                                                                                                                                                                                                            mail                                                                                                                                                                                                                                                            msgraph                                                                                                                                                                                                                                                            mysql                                                                                                                                                                                                                                                            oracle                                                                                                                                                                                                                                                            orm                                                                                                                                                                                                                                                            ormsearch                                                                                                                                                                                                                                                            pdf                                                                                                                                                                                                                                                            pmtagent                                                                                                                                                                                                                                                            postgresql                                                                                                                                                                                                                                                            presentation                                                                                                                                                                                                                                                            print                                                                                                                                                                                                                                                            redissessionstorage                                                                                                                                                                                                                                                            report                                                                                                                                                                                                                                                            saml                                                                                                                                                                                                                                                            scheduler                                                                                                                                                                                                                                                            search                                                                                                                                                                                                                                                            sharepoint                                                                                                                                                                                                                                                            spreadsheet                                                                                                                                                                                                                                                            sqlserver                                                                                                                                                                                                                                                            websocket                                                                                                                                                                                                                                                            zip                                                                                                                                                        名前                                                                              インストール済みのバージョン                                                                              カテゴリ                                                                              説明                                                                              必要な ColdFsuion パッケージ^n                                                                              必要な JAR                                                                                                  使用可能なバージョン                                                                                                  インストールのステータス                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Confirmation                   Packages Administrator  と  Adminapi  はアンインストールされません。\">                    すべてのパッケージをアンインストールしてもよろしいですか？  Packages Administrator  と  Adminapi  はアンインストールされません。                                                                                  OK                          キャンセル                                                                                                                  Confirmation                                       パッケージ  {}  をアンインストールしてもよろしいですか？                                                                                                         OK                          キャンセル                                                                                                                  Confirmation                                       すべてのパッケージをアップデートしてもよろしいですか？                                                                                                         OK                          キャンセル                                                                                使用可能なパッケージ                   インストールできるパッケージは他にありません。                                                                                                            名前                                                                              カテゴリ                                                                              説明                                                                              必要な ColdFsuion パッケージ^n                                                                              必要な JAR                                                                                                  使用可能なバージョン                                                                                                                      インストールのステータス                                                                                                                                                                                                                                                                                                                                                                                                                         Confirmation                                       すべてのパッケージをインストールしてもよろしいですか？                                                                                                        OK                          キャンセル                                                                                                                   Confirmation                                                                                                                                                                    OK                          キャンセル                                                                                                                          アンインストールの確認             アップデートをアンインストールしますか？     アンインストールプロセスの間に ColdFusion サーバーは停止されて再起動されます。                                                                                                進行状況情報                 アンインストールプロセスの間に ColdFusion サーバーは停止されて再起動されます。     アンインストールを開始しています... 時間がかかる場合があります           アンインストールのステータス : 確認しています...                                                                                                          進行状況情報                                                                                                                      進行状況情報                                                                                                            進行状況情報                                                                                                                                                                                                                                                              上書きを確認                       アップデートファイルは既に存在します。       既存のファイルを上書きしますか？                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                          ", "Link": "updates/index.cfm"}, {"Category": "パッケージマネージャー", "SubCategory": "設定", "Contents": "                                                                                                                                                         自動確認                           アップデートを自動的に確認                 選択すると、ログインのたびにアップデートが自動的に確認されます。                          通知                               アップデート確認間隔       日                         アップデートが使用可能な場合の、電子メール通知の送信先           (例 : <EMAIL>,<EMAIL>)                     アップデートが使用可能な場合の、電子メール通知の送信元               (例 : <EMAIL>)                          サイトを更新                     サイト URL                          ローカル更新サイトを設定してある場合は、そのサイトの URL を指定してアップデートを取得します。                       パッケージサイト                     サイト URL                          ローカルパッケージサイトを設定してある場合は、そのサイトの URL を指定してアップデートを取得します。                   プロキシ設定                             プロキシホスト                                                プロキシポート                                                プロキシユーザー名                                                プロキシパスワード                                                                       © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "updates/_settings.cfm"}, {"Category": "ライセンスとアクティベーション", "SubCategory": "アクティベーション", "Contents": "                                                                                                                                                                      ライセンスとアクティベーションページでは、所有している ColdFusion ライセンスを管理し、インスタンスの使用状況を追跡できます。               製品情報                                                                              サーバーエディション    デベロッパー                                                                            デプロイメントタイプ    Development                                                                                  Hostname    Parvathys-MacBook-Pro.local                                                        デバイス ID    29c8c3c0d5146c294ef6b2b9615fa0f0e5f419a3a8142b71d361215b5ae9e738                                                                                        ライセンス情報                                                                                                     アクティベーションモード     Named User License (NUL)                                                                                             アクティベーションステータス   アクティブでない                                                                                                                                                                                             ライセンスをアクティベート                                                                              ライセンスモード                                                                                                                    Named User License (NUL)                          Feature Restricted Licensing (Online/Offline)                          Feature Restricted Licensing (Isolated)                                                                                                                  デプロイメントタイプ                                                                                                                Staging                                                      Testing                                                      Development                                                      Disaster Recovery                                                      Production                                                                                                                                 アクティベーションコード                                                           BBBU7Y-KJWSS2-FSDDSQ                                                                                                                                                      このアクティベーションコードをコピーし Administrator コンソールで使用して、ライセンスファイルを生成してください                                                                                                ライセンスパス                                                                                                                                                                                                                                                            Activate                              アクティベート                                                                                                                                   オフラインでアクティベート               コンピューターが永続的にオフラインになっている (政府機関、銀行などのセキュアな環境に存在している) 場合は、オフラインアクティベーションモードを使用できます。              「アクティベーションリクエストを生成」ボタンをクリックしてアクティベーションリクエストファイルを生成し、次の場所にファイルをアップロードしてください。    https://www.adobe.com/go/coldfusion-activate                オフラインアクティベーションの場合は上記のシリアル番号を入力してください。               アクティベーションリクエストを生成                                                  上記手順のアクティベーション応答ファイルを使用し、「送信」をクリックして ColdFusion をアクティベートしてください。                                           アクティベーション応答 :                                   アップロード                                        送信                                          注意 - アクティベーションプロセスを  72 時間 以内に完了できない場合は、新しいアクティベーションリクエストを生成する必要があります。                                                               確認                                       ColdFusion (2023 リリース) のライセンス認証を解除します。解除後、ColdFusion はデベロッパー版または体験版に戻ります。                      続行しますか？                                                                                   はい                          いいえ                                                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "activation/activation.cfm"}, {"Category": "ライセンスとアクティベーション", "SubCategory": "使用状況", "Contents": "                                                                                                                                                                           使用状況ページには、使用されているインスタンスの数、使用ユニット数、使用状況グラフなど、ライセンスの使用状況の様々な分析結果が表示されます。    使用状況データを表示するには、必要な日付範囲を選択し、「使用状況を追跡」をクリックしてください。                        開始日                                  終了日                                       使用状況を追跡                                PDF に書き出し                                   ライセンスエンドポイントが到達不能なので、使用状況データを取得できません。アクティブなインターネット接続が必要です。                     ライセンス使用状況データ                 合計アクティベーション数   :                                         サーバーインスタンス         Hostname/Container ID         コア数                デプロイメントタイプ         ライセンス認証済み         アクティベーション / アクティベーション解除日         Docker (はい / いいえ)                                       合計アクティベーション数          使用可能なデータがありません。                                         Docker の使用状況       使用可能なデータがありません。                                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "activation/usage.cfm"}, {"Category": "ライセンスとアクティベーション", "SubCategory": "設定", "Contents": "                                                                                                                                                       通知                         サブスクリプションの有効期限が近づいたら、メールで通知を受け取ることができます。最初の通知は、サブスクリプションの有効期限が切れる 90 日前に送信され、それ以降の通知は、以下で選択した頻度に応じて送信されます。なお、以下のオプションにチェックを入れ、メールサーバーを設定してある場合にのみ、通知を受け取ることができます。                                                     通知間隔 :        日 (猶予期間終了まで)                        電子メール通知の送信先          (例 : <EMAIL>,<EMAIL>)                    電子メール通知の送信元              (例 : <EMAIL>)                                 インスタンスを自動的に再起動                                         アクティベーション後、アクティベーション解除後または使用状況データ設定の変更後にインスタンスを自動的に再起動させる場合に有効にします。                                       プロキシサーバー設定                                       プロキシサーバーを使用するには、 パッケージマネージャーの設定 ページで設定を指定してください。                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "activation/settings.cfm"}]
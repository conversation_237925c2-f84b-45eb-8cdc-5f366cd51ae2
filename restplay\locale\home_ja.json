{"PageTitle": "REST Playground", "PageTitleBar": "ColdFusion REST Playground", "Applications": "アプリケーション", "AddApp": "アプリケーションを追加", "Search": "検索", "ShowError": "クリックしてエラーを確認", "RefreshApp": "アプリケーションを更新", "NoAppAvailable": "アプリケーションなし", "Request": "リクエスト", "URLCopied": "URL がクリップボードにコピーされました", "PressCopy": "コピーするには Ctrl+C を押します", "Headers": "ヘッダー", "Parameters": "パラメーター", "Body": "本文", "Name": "名前", "Value": "値", "AddHeader": "ヘッダーを追加", "AddParameter": "パラメーターを追加", "AddValue": "値を追加", "Send": "送信", "Response": "レスポンス", "Status": "ステータス", "Documentation": "ドキュメント", "Description": "説明", "NoDesc": "説明なし", "NoParamsAvailable": "パラメーターなし", "ParamName": "名前", "ParamType": "タイプ", "ParamRealType": "パラメータータイプ", "AppPath": "アプリケーションのパス", "AppHost": "ホスト", "ServiceMapping": "サービスマッピング", "SetDefaultApp": "デフォルトアプリケーションとして設定する", "Register": "登録", "Cancel": "キャンセル", "EnableDevProfile": "ColdFusion Administrator で（「デバッグとロギング」で）デベロッパープロファイルを有効にします。これにより、依存するすべての設定（REST 確認／信頼できるキャッシュ／Robust 例外）が更新されます。デベロッパープロファイルの有効化は本番環境やロックダウン環境では推奨されません。", "ErrorFetchApplications": "アプリケーションの取得中にエラーが発生しました", "DeleteApp": "アプリケーションを削除", "DeleteAppName": "アプリケーション {} を削除しますか？", "CouldNotDelete": "アプリケーションを削除できませんでした", "ErrorUpdateApps": "アプリケーションの更新中にエラーが発生しました", "ErrorFetchConf": "設定の取得中にエラーが発生しました。", "EnterValidApp": "有効なアプリケーションのパスを入力してください。", "ServiceNotRegistered": "サービスを登録できませんでした", "ServiceRegistered": "サービスが正常に登録されました", "RegisterApp": "アプリケーションを登録", "NoAppMsg": "REST API を使用して、起動する ColdFusion アプリケーションを登録してください。<br/>現在登録されているアプリケーションはありません。", "RequestSummary": "リクエストの要約", "URL": "リクエスト URL", "Method": "リクエストメソッド", "TipAppPath": "CFC が保存されているアプリケーションパスまたはルートフォルダー", "TipAppHost": "REST サービスのホスト名。例 : localhost:8500 (オプション)", "TipServiceMapping": "REST サービスを呼び出す際のアプリケーション名に代替文字列を使用します。例 : http://localhost/rest/{サービスマッピング}/test (オプション)", "TipDefaultApp": "Web サービスの呼び出し中に URL のアプリケーション名を除外するには、アプリケーションをデフォルトに設定します。ホストで許可されるデフォルトアプリケーションは 1 つです。例 : http://localhost/rest/path", "Error": "エラー"}
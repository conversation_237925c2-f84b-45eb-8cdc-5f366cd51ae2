/* Top container.... */
.cf-upload-container {
	position: relative;
	left:14px;
	padding: 10px; 
	border: 1px solid silver;
	background-color: #D3E6EA; 	
}

/* top button container */
.cf-upload-buttons-ct { 
	position:relative;
	height: 22px;
	margin-bottom: 10px;
}

/* top button containe  */
.cf-upload-input-ct {
	position:absolute;
	height: 22px;
	width: 78px;
	overflow: hidden;
}

/* browse button */
.cf-upload-bbtn-ct {
	position: absolute;
	height: 22px;
	right:0;
}

/*  File input fields */
.cf-upload-input-wrap {
	position: absolute;
	opacity: 0.0;
	-moz-opacity: 0.0;
	filter: alpha(opacity=0);	
	right:0;
	height: 22px;
}

/*  individual input */
.cf-upload-input-wrap input {
	top: 0;
	height: 22px;
	font-size: 14px;
	cursor: pointer;
	-moz-user-focus: ignore;
	border: 0px none transparent;

	overflow: hidden;
}

 /* botton button container   */
 .cf-upload-bottom-p {
 	position:relative;
	height: 22px;
	margin-left:100px;
	right:0;
	overflow: hidden; 
 }

/* upload button */
.cf-upload-ubtn-ct {
	position: absolute;
	right:75px;
}

/* clear button */
.cf-upload-cbtn-ct {
	position: absolute;
	right:0;
}

/*  File list table */ 
.cf-upload-table {
	width:100%;
	font-size: 11px;
	border-collapse: collapse;
	margin-top: 5px;
	margin-bottom:20px;
}


.cf-upload-input-mask {
	position:absolute;
	height: 22px;
	width: 100%;
	left: -18px;
}


.cf-upload-filename {
	-moz-user-select: none;
	cursor: default;
	padding-left: 3px;
}

.cf-upload-filedelete {
	width: 16px;
	text-align: right;
	padding-right: 2px;
}
.cf-upload-mask {
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0.6;
	-moz-opacity: 0.6;
	filter: alpha(opacity=60);	
	background-color: white;
}
.cf-upload-wait {
	width: 16px;
	height: 16px;
	background: transparent url(../../extjs/resources/images/default/grid/wait.gif);
	background-repeat: no-repeat;
	position:absolute;
	right: 56px;
	top: 2px;
	display:none;
}

/* progress bar wrapper  */
.cf-upload-progress-wrap {
	margin-top:10px;
	margin-bottom:2px;
	border: 1px solid #6593cf;
	overflow:hidden;
}

/*  progress bar*/

.cf-upload-progress {
	height: 6px;
    background: #e0e8f3 url(/CFIDE/scripts/ajax/resources/ext/images/default/qtip/bg.gif) repeat-x;
}

.cf-upload-progress-text {
	margin-top:5px;
	height: 20px;
    width:100%;
	
}

/* progress bar upload.. */
.cf-upload-progress-bar {
	height:5px;
	overflow:hidden;
	width:0;
	background:#8BB8F3;
	border-top:1px solid #B2D0F7;
	border-bottom:1px solid #65A1EF;
	border-right:1px solid #65A1EF;
}
.cf-upload-pginfo-ct {
	font-size: 11px;
	font-family: tahoma,verdana,helvetica;
	border: 1px solid #65A1EF;
	background: #ffffe8;
	padding: 2px;
}
.cf-upload-pginfo-table {
	width: 100%;
	border: 0;
}
.cf-upload-pginfo-value {
	text-align: right;
/*	font-weight: bold; */
}

.cf-upload-filepb {
	line-height: 1px;
	font-size: 1px;
	height: 1px;
	background:#8BB8F3;
	border-top:1px solid #B2D0F7;
	border-bottom:1px solid #65A1EF;
	border-right:1px solid #65A1EF;
	width: 0;
}
.cf-upload-layer {
	border: 1px solid silver;
	background: #f4f4ff;
}
.cf-upload-layer-form-ct {
	margin: 8px;
}


/* end of file */

<cfsilent>
<!--- Initialize variables.settings structure  --->
<cfset variables.settings = StructNew()>
<!--- absolute path to User's File storage folder  --->
<cfset variables.settings.UserFiles 		= ExpandPath("/cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles")> <!--- like #ExpandPath('../../../../UserFiles')# --->
<!--- URL to user's file storage folder            --->
<cfset variables.settings.UserFilesURL	= "/cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles"> <!--- like : http://myste.com/UserFiles --->
<!--- image size for thubnail images    --->
<cfset variables.settings.thumbSize		= 120>
<!--- image size for medium size images --->
<cfset variables.settings.middleSize		= 250>
<!--- Permision for linux               --->
<cfset variables.settings.chomd			= "777">
<!--- disallowed file types             --->
<cfset variables.settings.disfiles		= "as,asp,aspx,bin,cfc,cfm,cfml,cfr,cfsw,class,dmg,exe,hbxml,jar,jsp,jspx,jwx,msxml,php,swc,sws,asmx,ashx">
<!--- Disable file uploads by default--->
<cfset variables.settings.AllowUploads = "false">
<!--- Disable Directory Creation by default --->
<cfset variables.settings.AllowDirectoryCreation = "false">
<!--- Disable File Manager Directory Navigation By Default --->
<cfset variables.settings.AllowDirectoryNavigation = "false">
</cfsilent>

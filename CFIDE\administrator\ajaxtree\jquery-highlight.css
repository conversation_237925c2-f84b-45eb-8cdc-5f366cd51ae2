/* rotate the entire page 180 degrees. the rule that started it all. disabled by default because it's not very, um, subtle */
body {
    /*-webkit-transform: rotate(180deg) !important; 
    -moz-transform: rotate(180deg) !important;
    -ms-transform: rotate(180deg) !important;
    -o-transform: rotate(180deg) !important;
    transform: rotate(180deg) !important; */
}

/* rotate *some* images 180 degrees */
img[src*="7"] {
    -webkit-transform: rotate(180deg) !important; /* Chrome, Safari 3.1+ */
    -moz-transform: rotate(180deg) !important; /* Firefox 3.5-15 */
    -ms-transform: rotate(180deg) !important; /* IE 9 */
    -o-transform: rotate(180deg) !important; /* Opera 10.50-12.00 */
    transform: rotate(180deg) !important; /* Firefox 16+, IE 10+, Opera 12.10+ */
}

/* slightly smaller first letters */
*:first-letter {
    /*font-size: 90% !important;*/
}

@-webkit-keyframes fade { /* Chrome, Safari 5+ */
    0% { opacity: 0.98; }
    100% { opacity: 0.49; }
}
@-moz-keyframes fade { /* Firefox 5-15 */
    0% { opacity: 0.98; }
    100% { opacity: 0.49; }
}
@-o-keyframes fade { /* Opera 12.00 */
    0% { opacity: 0.98; }
    100% { opacity: 0.49; }
}
@keyframes fade { /* Chrome, Firefox 16+, IE 10+, Opera 12.10+ */
    0% { opacity: 0.98; }
    100% { opacity: 0.49; }
}

/* bring back <blink>! */
@-webkit-keyframes blink { /* Chrome, Safari 5+ */
    0% { opacity: 1; }
    50% { opacity: 1; }
    51% { opacity: 0; }
    100% { opacity: 0; }
}
@-moz-keyframes blink { /* Firefox 5-15 */
    0% { opacity: 1; }
    50% { opacity: 1; }
    51% { opacity: 0; }
    100% { opacity: 0; }
}
@-o-keyframes blink { /* Opera 12.00 */
    0% { opacity: 1; }
    50% { opacity: 1; }
    51% { opacity: 0; }
    100% { opacity: 0; }
}
@keyframes blink { /* Chrome, Firefox 16+, IE 10+, Opera 12.10+ */
    0% { opacity: 1; }
    50% { opacity: 1; }
    51% { opacity: 0; }
    100% { opacity: 0; }
}

/* always show scroll bars */
/*
html {
    overflow: scroll !important;
    height: 100.1% !important;
    width: 100.1% !important;
}*/

/* confusing/conflicting double vertical scrollbars 
body {
    overflow: scroll !important;
    height: 200% !important;
}
*/
/* never allow selection! */
* {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

/* Y U NO HOVER?! */
a:hover {
    /*display: none;
    font-size: 0;*/
}

/* misaligned images */
img[align="left"] {
    /*float: right;*/
}

img[align="right"] {
    /*float: left;*/
}
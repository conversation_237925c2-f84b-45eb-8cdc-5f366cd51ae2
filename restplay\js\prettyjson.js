/** 
* @license ng-prettyjson - v0.1.8
* (c) 2013 Julien VALERY https://github.com/darul75/ng-prettyjson
* License: MIT 
**/
!function(a){"use strict";a.module("ngPrettyJson",[]).directive("prettyJson",["$compile","$templateCache","ngPrettyJsonFunctions",function(b,c,d){var e=a.isDefined;return{restrict:"AE",scope:{json:"=",prettyJson:"=",onEdit:"&"},template:"<div></div>",replace:!0,link:function(f,g,h){var i={},j=null;f.id=h.id||"prettyjson",f.editActivated=!1,f.edition=h.edition,f.aceEditor=void 0!==window.ace;// compile template
var k=b(c.get("ng-prettyjson/ng-prettyjson-panel.tmpl.html"))(f,function(a,b){b.tmplElt=a});g.removeAttr("id"),g.append(k);// prefer the "json" attribute over the "prettyJson" one.
// the value on the scope might not be defined yet, so look at the markup.
var l,m=e(h.json)?"json":"prettyJson",n=function(a){var b=d.syntaxHighlight(a)||"";return b=b.replace(/\{/g,"<span class='sep'>{</span>").replace(/\}/g,"<span class='sep'>}</span>").replace(/\[/g,"<span class='sep'>[</span>").replace(/\]/g,"<span class='sep'>]</span>").replace(/\,/g,"<span class='sep'>,</span>"),e(a)?f.tmplElt.find("pre").html(b):f.tmplElt.find("pre").empty()};l=f.$watch(m,function(b){// BACKWARDS COMPATIBILITY:
// if newValue is an object, and we find a `json` property,
// then stop watching on `exp`.
a.isObject(b)&&e(b.json)?(l(),f.$watch(m+".json",function(a){f.editActivated||n(a),i=a},!0)):(f.editActivated||n(b),i=b),j&&(j.removeListener("change",o),j.setValue(JSON.stringify(b,null,"	")),j.on("change",o),j.resize())},!0);var o=function(){try{i=JSON.parse(j.getValue()),f.parsable=!0}catch(a){f.parsable=!1}// trigger update
f.$apply(function(){})};f.edit=function(){return f.aceEditor?(f.editActivated?(j&&(document.getElementById(f.id).env=null),n(i)):(j=ace.edit(f.id),j.setAutoScrollEditorIntoView(!0),j.setOptions({maxLines:1/0}),j.on("change",o),j.getSession().setMode("ace/mode/json")),void(f.editActivated=!f.editActivated)):void(console&&console.log("'ace lib is missing'"))},f.update=function(){f[m]=i,f.$emit("json-updated",i),f.onEdit&&f.onEdit({newJson:i}),this.edit()}}}}]).factory("ngPrettyJsonFunctions",function(){// cache some regular expressions
var b={entities:/((&)|(<)|(>))/g,json:/"(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|(null))\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g},c=["&amp;","&lt;","&gt;"],d=["number","string","key","boolean","null"],e=function(){var a=arguments.length-2;do a--;while(!arguments[a]);return a},f=function(a){var b;// the final two arguments are the length, and the entire string itself;
// we don't care about those.
if(arguments.length<7)throw new Error("markup() must be called from String.prototype.replace()");return b=e.apply(null,arguments),'<span class="'+d[b]+'">'+a+"</span>"},g=function(){var a;if(arguments.length<5)throw new Error("makeEntities() must be called from String.prototype.replace()");return a=e.apply(null,arguments),c[a-2]},h=function(c){return a.isString(c)||(c=JSON.stringify(c,null,2)),a.isDefined(c)?c.replace(b.entities,g).replace(b.json,f):void 0};return{syntaxHighlight:h,makeEntities:g,markup:f,rx:b}})}(window.angular),function(a){"use strict";a.module("ngPrettyJson").run(["$templateCache",function(a){a.put("ng-prettyjson/ng-prettyjson-panel.tmpl.html",'<div><button ng-click="edit()" ng-show="edition && !editActivated">Edit</button><button ng-click="edit()" ng-show="edition && editActivated">Cancel</button><button ng-click="update()" ng-show="editActivated && parsable">Update</button><pre class="pretty-json" id="{{id}}"></pre></div>')}])}(window.angular);
/* ADOBE SYSTEMS INCORPORATED Copyright 2007 Adobe Systems Incorporated All Rights Reserved. 
 NOTICE:Adobe permits you to use, modify, and distribute this file in accordance with the
 terms of the Adobe license agreement accompanying it. If you have received this file from 
 a source other than Adobe, then your use, modification, or distribution of it requires the 
 prior written permission of Adobe.*/


/************************************************/
/*****cfdialog.css*/
/***********************************************/

/* Dialog styles */

yuiextdlg{
visibility:hidden;
position:absolute;
top:0px;    
}

/************************************************/
/*****cfpod.css*/
/***********************************************/
.ypod{
	z-index:1;
	overflow:hidden;
}

.ypod .ypod-hd {
	background: url(../ext/images/default/basic-dialog/hd-sprite_flex.gif) repeat-x 0px -82px;
	color:#FFF;
	overflow:hidden;
	font:bold 12px "sans serif", tahoma, verdana, helvetica;
	padding:5px;
}

.ypod .ypod-hd-left {
	background: url(../ext/images/default/basic-dialog/hd-sprite_flex.gif) no-repeat 0px -41px;
	padding-left:3px;
	margin:0px;
}

.ypod .ypod-hd-right {
	background: url(../ext/images/default/basic-dialog/hd-sprite_flex.gif) no-repeat right 0px;
	padding-right:3px;
}

.ypod .ypod-bgcolor-hd {
	color:#FFF;
	font:bold 12px "sans serif", tahoma, verdana, helvetica;
	padding-bottom:5px;
	padding-left:3px;
}

.ypod .ypod-dlg-body{
	background-color:white;
	border:3px solid #C1CCD0;
	border-top:0px none;
	padding:10px;
}


/************************************************/
/*****cfautosuggest.css*/
/***********************************************/
	div.autosuggest
	{
		position:relative;
		float:left;
	}

	input.autosuggestinput
	{
		z-index:0;
	}
	
	div.autosuggestcontainer 
	{	
		position:absolute;
		top:1.5em;
		width:100%;	
	}

    div.autosuggestcontainer .yui-ac-content
	{
		position:absolute;
		width:100%;border:1px solid #CCCCCC;
		background:#fff;overflow:hidden;
		z-index:9050;
	}

    div.autosuggestcontainer .yui-ac-shadow
	{
		position:absolute;
		margin:.3em;width:100%;
		background:#CCCCCC;
		z-index:9049;
	}

    div.autosuggestcontainer ul
	{
		padding:5px 0;
		width:100%;
		margin:0;
		padding:0;
	}

    div.autosuggestcontainer li
	{
		padding:0 5px;
		width:100%;
		cursor:default;
		white-space:nowrap;
		margin:0;
		padding:0;
	}

    div.autosuggestcontainer li.yui-ac-highlight
	{
		background:#7FCDFE;
	}

    div.autosuggestcontainer li.yui-ac-prehighlight
	{
		background:#AADEFF;
	}

/**************************************/
/** cfcalendar.css **/
/***************************************/
	input.datefieldinput
	{
		z-index:0;
	}

/**************************************/
/** cftoolbar.css **/
/***************************************/
.ytoolbar .cftoolbarbutton { 
	width:100%;	
}

/**************************************/
/** corners css **/
/***************************************/
b.corner{display:block;background: transparent}
b.corner b{display:block;height: 1px;
    overflow: hidden; background: transparent}
b.filler1{margin: 0 5px}
b.filler2{margin: 0 3px}
b.filler3{margin: 0 2px}
b.corner b.filler4{margin: 0 1px;height: 2px}

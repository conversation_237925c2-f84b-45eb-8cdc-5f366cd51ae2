/*
 * Copyright (c) 1995-2005 Macromedia, Inc. All rights reserved. 
*/
.cfform{
	background-color: #CC0000;
	width: 98%;
	height: auto;
	font-family: Arial, Helvetica, sans-serif;
	padding:10px;
}
.cfform table{
	background-color: #FFCCCC;
	font-family: Arial, Helvetica, sans-serif;
}
.cfform hr{	
	width: 99%;
	height: 1px;
	color: #cabba9;
}
.cfform p{
	margin-top: 0px;
	padding: 0px;
}

/**********************
 Form Elements 
***********************/
.cfElementRow{
	padding-top:3px;
	padding-bottom:2px;
}

.cfInput {
	font-size: 12px;
	font-family: Arial, Helvetica, sans-serif;
	margin-left: 5px;
	margin-right: 5px;
	width:200px;
}

.cfTextarea {
	font-size: 12px;
	font-family: Arial, Helvetica, sans-serif;
	margin-left: 50px;
	margin-right: 5px;
	width:50%;
}

.cfSelect {
	font-size: 12px;
	font-family: Arial, Helvetica, sans-serif;
	margin-left: 5px;
	margin-right: 5px;
}


.cfRadio{
	font-size: 12px;
	font-family: Arial, Helvetica, sans-serif;
}

.cfCheckbox{
	font-size: 12px;
	font-family: Arial, Helvetica, sans-serif;
}

.cfButton{
	background-color: #FFCCCC;
	width: 80px;	
	color: #000000;
	font-weight:bold;
	font-family: inherit;
	margin-bottom:10px;
	margin-right:10px;
	margin-top:10px;
}

.cfButtonBar{

}

.cfText{
	font-size: 12px;
	font-family: Arial, Helvetica, sans-serif;
	padding:10px !important;
}


.cfRequiredElement{

}

/**************************
 labels 
 **************************/
/* label of form element */
label{
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	padding-right:2px;
}

/* first child in a horizontal group, sets width so all elements down form line up */
.cfFirstChild  {
	width: 130px !important; 
}

/* the label of an individual form element */
.cfLabelTitle{
	color: #000000;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	margin-top: 8px;
	width:100px;
}

/* applyes to vertical form group labels */
.cfHeaderTitle{
	width: 100%;
	height: 40px;
	vertical-align: baseline;
	padding-top: 10px;
	padding-left: 25px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 16px;
	color: #000000;
	background-color: #FFCCCC;
	border-top: 1px solid #CC0000;
	border-bottom: 1px solid #CC0000;
}

/* applys to label of elements that are required */
.cfRequiredLabel{
	font-weight: bold;
}

.cfRequiredLabelIndicator {
    color: #ff0000;
    font-size:1.2em;
}

/***************************
	alignment 
****************************/
/* horizontal form groups */
.horizontal{	

}

/* vertical form groups */
.vertical table{	

}

/* left aligned labels */
.cfLabelPosLeft  {
	margin-left: 0px !important;
	text-align:left;
}

/* right aligned labels */
.cfLabelPosRight {
	margin-right: 10px;
	text-align: right;
}

/* top aligned labels */
.cfLabelPosTop  {
	margin-right: 1ex;
	padding-left:50px;
}

/* centered aligned labels */
.cfLabelPosCenter  {
	margin-right: 1ex;
	text-align: center;
}




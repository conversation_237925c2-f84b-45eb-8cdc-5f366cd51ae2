/*
All of the code within the ZingChart software is developed and copyrighted by ZingChart, Inc., and may not be copied,
replicated, or used in any other software or application without prior permission from ZingChart. All usage must coincide with the
ZingChart End User License Agreement which can be requested by <NAME_EMAIL>.

Build 2.9.14-hf.1
*/
zingchart.setModule("boxplot"),ZC.ZCBoxPlot={getDefaults:function(e,o){var l={},t=(ZC._cp_(ZC.ZCBoxPlot.AO,l),l.palette),o=(ZC.ZCBoxPlot.KE[o]&&(ZC._cp_(ZC.ZCBoxPlot.KE[o],l),t=ZC.ZCBoxPlot.KE[o].palette),t[e%t.length]);return l.box["border-color"]=o,l.outlier.marker["border-color"]=o,l.level["line-color"]=o,l.connector["line-color"]=o,l},AO:{palette:["#89b92e","#0392bb","#cc3300","#da9b04","#6e4503","#1540a0"],box:{"background-color":"#fff","border-width":1,"border-color":"#89b92e","hover-state":{"background-color":"transparent"}},outlier:{marker:{"background-color":"#fff","border-width":1,"border-color":"#89b92e",size:6}},level:{"line-width":2,"line-style":"solid","line-color":"#89b92e"},connector:{"line-width":1,"line-style":"solid","line-color":"#89b92e"}},KE:{light:{palette:["#29A2CC","#7CA82B","#D31E1E","#EF8535","#A14BC9","#A05F18","#265E96","#6B7075"],box:{"background-color":"#29A2CC","border-width":1,"border-color":"#29A2CC","hover-state":{"background-color":"transparent"}},outlier:{marker:{"background-color":"#29A2CC","border-width":1,"border-color":"#fff",size:4}},level:{"line-width":2,"line-style":"solid","line-color":"#fff"},"line-median-level":{"line-color":"#ffffff","line-width":2},connector:{"line-width":1,"line-style":"solid","line-color":"#29A2CC"}},dark:{palette:["#29A2CC","#7CA82B","#D31E1E","#EF8535","#A14BC9","#A05F18","#265E96","#6B7075"],box:{"background-color":"#29A2CC","border-width":1,"border-color":"#29A2CC","hover-state":{"background-color":"transparent"}},outlier:{marker:{"background-color":"#29A2CC","border-width":1,"border-color":"#fff",size:4}},level:{"line-width":2,"line-style":"solid","line-color":"#221F1F"},"line-median-level":{"line-color":"#221F1F","line-width":2},connector:{"line-width":1,"line-style":"solid","line-color":"#29A2CC"}}}},zingchart.bind(null,"dataparse",function(e,o){for(var l=e.theme,t=0,i=o.graphset.length;t<i;t++)if(o.graphset[t].type&&("boxplot"===o.graphset[t].type||"hboxplot"===o.graphset[t].type)){for(var n=o.graphset[t].type,a=(o.graphset[t]["plugin-type"]=n,o.graphset[t]),r=(ZC._todash_(a),{}),p=(a.options&&(r=a.options[n]||a.options),ZC._todash_(r),a["scale-x"]=a["scale-x"]||{},a["scale-x"].values=a["scale-x"].values||[],a["scale-y"]=a["scale-y"]||{},a["scale-y"]["min-value"]=null!==ZC._n_(a["scale-y"]["min-value"])?a["scale-y"]["min-value"]:"auto",a.series=a.series||[],a.series[0]=a.series[0]||{},a.series[0].values=a.series[0].values||[],a.type="boxplot"===n?"mixed":"hmixed",a.plot=a.plot||{},a.plot.mode="normal",a.tooltip=a.tooltip||{},a.tooltip["background-color"]=a.tooltip["background-color"]||"#29A2CC",a.series),c=(ZC._todash_(p),0),s="",d=0;d<p.length;d++)if(null===ZC._n_(p[d].type)||"boxplot"===p[d].type){var h=p[d]["data-box"]||[],x=p[d]["data-outlier"]||[],b=p[d].options||{},_=ZC.ZCBoxPlot.getDefaults(c,l),g={id:"boxplot-bar-"+d,type:"boxplot"===n?"vbar":"hbar",mode:"normal",scales:p[d].scales,options:b};for(s in ZC._cp_(_.box,g),ZC._cp_(p[d],g),p[d])0===s.indexOf("data-")&&"data-box"!==s&&"data-outlier"!==s&&(g[s]=p[d][s]);for(var u=[],C=[],y=[],Z=[],f=[],v=[],m=[],A=0,w=h.length;A<w;A++){var B=A,k=0,z=(6===h[A].length&&(B=h[A][0],k=1),h[A][0+k]),E=h[A][1+k],F=h[A][2+k],j=h[A][3+k],k=h[A][4+k];u.push([B,j-E,z-E,k-E,F-E]),C.push(E),y.push(z),Z.push(k),f.push(F),m.push(E),v.push(j)}g.values=u,g["extra-values"]=3,g["offset-values"]=C,g["data-min"]=y,g["data-max"]=Z,g["data-median"]=f,g["data-lower-quartile"]=m,g["data-upper-quartile"]=v,ZC._cp_(r.box,g),ZC._cp_(b.box,g);var K={id:"boxplot-scatter"+d,type:"boxplot"===n?"scatter":"hscatter",mode:"normal",scales:p[d].scales,options:b,values:x};for(s in ZC._cp_(_.outlier,K),ZC._cp_(r.outlier,K),ZC._cp_(b.outlier,K),p[d])0===s.indexOf("data-")&&"data-box"!==s&&"data-outlier"!==s&&(K[s]=p[d][s]);p.push(g),x.length&&p.push(K),c++}for(d=p.length-1;0<=d;d--)null!==ZC._n_(p[d].type)&&"boxplot"!==p[d].type||p.splice(d,1)}return o}),zingchart.bind(null,"legend_item_click",function(e){var o=zingchart.getLoader(e.id),o=zingchart.getGraph(o,e.graphid).o;if(o["plugin-type"]=o["plugin-type"]||"","boxplot"===o["plugin-type"]||"hboxplot"===o["plugin-type"])for(var l=zingchart.exec(e.id,"getobjectsbyclass",{type:"shape",graphid:e.graphid,cls:"boxplot-line-"+e.plotindex}),t=0,i=l.length;t<i;t++)zingchart.exec(e.id,"updateobject",{type:"shape",graphid:e.graphid,id:l[t],data:{alpha:e.visible?0:1}})}),zingchart.bind(null,"objectsinit",function(e){var o=zingchart.getLoader(e.loader.id),l=zingchart.getGraph(o,e.graphid),t=l.o,i=o.K1;if(t["plugin-type"]=t["plugin-type"]||"","boxplot"===t["plugin-type"]||"hboxplot"===t["plugin-type"]){var n={},a=(t.options&&(n=t.options[t["plugin-type"]]||t.options),ZC._todash_(n),t.shapes||[]);for(g=a.length-1;0<=g;g--)-1!==a[g]["class"].indexOf("boxplot-line-")&&a.splice(g,1);var r=[];for(g=0;g<l.BB.A7.length;g++)-1!==l.BB.A7[g].K8.indexOf("boxplot-bar")&&r.push(g);for(var p=t.series,c=(ZC._todash_(p),0),s=0;s<r.length;s++){for(var d=r[s],h=zingchart.exec(e.loader.id,"getobjectinfo",{object:"plot",graphid:l.L,plotindex:d}),x=l.AY(h.scales[1]),b=p[d].options||{},_=ZC.ZCBoxPlot.getDefaults(c,i),g=0;g<l.BB.A7[d].K.length;g++){var u,C,y,Z,f,v,m,A=zingchart.exec(e.loader.id,"getobjectinfo",{object:"node",graphid:l.L,plotindex:d,nodeindex:g});A.onviewport&&A.visible&&("boxplot"===t["plugin-type"]?(u=ZC._i_(x.AB(A.xdata.min)),C=ZC._i_(x.AB(A.xdata.median)),y=ZC._i_(x.AB(A.xdata.max))):(Z=ZC._i_(x.AB(A.xdata.min)),f=ZC._i_(x.AB(A.xdata.median)),v=ZC._i_(x.AB(A.xdata.max))),m={type:"line",id:"boxplot-line-max-connector-"+d+"-"+g,"class":"boxplot-line-"+d,flat:!1,points:"boxplot"===t["plugin-type"]?[[A.x+A.width/2,x.AI?A.y+A.height:A.y],[A.x+A.width/2,y]]:[[x.AI?A.x:A.x+A.width,A.y+A.height/2],[v,A.y+A.height/2]]},ZC._cp_(_.connector,m),ZC._cp_(n.line,m),ZC._cp_(n["line-max-connector"],m),ZC._cp_(b["line-max-connector"],m),a.push(m),m={type:"line",id:"boxplot-line-max-level-"+d+"-"+g,"class":"boxplot-line-"+d,flat:!1,points:"boxplot"===t["plugin-type"]?[[A.x+.25*A.width,y],[A.x+.75*A.width,y]]:[[v,A.y+.25*A.height],[v,A.y+.75*A.height]]},ZC._cp_(_.level,m),ZC._cp_(n.line,m),ZC._cp_(n["line-max-level"],m),ZC._cp_(b["line-max-level"],m),a.push(m),m={type:"line",id:"boxplot-line-min-connector-"+d+"-"+g,"class":"boxplot-line-"+d,flat:!1,points:"boxplot"===t["plugin-type"]?[[A.x+A.width/2,x.AI?A.y:A.y+A.height],[A.x+A.width/2,u]]:[[x.AI?A.x+A.width:A.x,A.y+A.height/2],[Z,A.y+A.height/2]]},ZC._cp_(_.connector,m),ZC._cp_(n.line,m),ZC._cp_(n["line-min-connector"],m),ZC._cp_(b["line-min-connector"],m),a.push(m),m={type:"line",id:"boxplot-line-min-level-"+d+"-"+g,"class":"boxplot-line-"+d,flat:!1,points:"boxplot"===t["plugin-type"]?[[A.x+.25*A.width,u],[A.x+.75*A.width,u]]:[[Z,A.y+.25*A.height],[Z,A.y+.75*A.height]]},ZC._cp_(_.level,m),ZC._cp_(n.line,m),ZC._cp_(n["line-min-level"],m),ZC._cp_(b["line-min-level"],m),a.push(m),m={type:"line",id:"boxplot-line-median-level-"+d+"-"+g,"class":"boxplot-line-"+d,flat:!1,points:"boxplot"===t["plugin-type"]?[[A.x,C],[A.x+A.width-.5,C]]:[[f,A.y],[f,A.y+A.height-.5]]},ZC._cp_(_.level,m),ZC._cp_(_["line-median-level"],m),ZC._cp_(n.line,m),ZC._cp_(n["line-median-level"],m),ZC._cp_(b["line-median-level"],m),a.push(m))}c++}t.shapes=a}});
/*
All of the code within the ZingChart software is developed and copyrighted by ZingChart, Inc., and may not be copied,
replicated, or used in any other software or application without prior permission from ZingChart. All usage must coincide with the
ZingChart End User License Agreement which can be requested by <NAME_EMAIL>.

Build 2.9.14-hf.1
*/
zingchart.setModule("boxplot"),ZC.ZCBoxPlot={getDefaults:function(e,o){var t={},l=(ZC._cp_(ZC.ZCBoxPlot.B6,t),t.palette),o=(ZC.ZCBoxPlot.NS[o]&&(ZC._cp_(ZC.ZCBoxPlot.NS[o],t),l=ZC.ZCBoxPlot.NS[o].palette),l[e%l.length]);return t.box[ZC._[61]]=o,t.outlier.marker[ZC._[61]]=o,t.level["line-color"]=o,t.connector["line-color"]=o,t},B6:{palette:["#89b92e","#0392bb","#cc3300","#da9b04","#6e4503","#1540a0"],box:{"background-color":"#fff","border-width":1,"border-color":"#89b92e","hover-state":{"background-color":"transparent"}},outlier:{marker:{"background-color":"#fff","border-width":1,"border-color":"#89b92e",size:6}},level:{"line-width":2,"line-style":"solid","line-color":"#89b92e"},connector:{"line-width":1,"line-style":"solid","line-color":"#89b92e"}},NS:{light:{palette:["#29A2CC","#7CA82B","#D31E1E","#EF8535","#A14BC9","#A05F18","#265E96","#6B7075"],box:{"background-color":"#29A2CC","border-width":1,"border-color":"#29A2CC","hover-state":{"background-color":"transparent"}},outlier:{marker:{"background-color":"#29A2CC","border-width":1,"border-color":"#fff",size:4}},level:{"line-width":2,"line-style":"solid","line-color":"#fff"},"line-median-level":{"line-color":"#ffffff","line-width":2},connector:{"line-width":1,"line-style":"solid","line-color":"#29A2CC"}},dark:{palette:["#29A2CC","#7CA82B","#D31E1E","#EF8535","#A14BC9","#A05F18","#265E96","#6B7075"],box:{"background-color":"#29A2CC","border-width":1,"border-color":"#29A2CC","hover-state":{"background-color":"transparent"}},outlier:{marker:{"background-color":"#29A2CC","border-width":1,"border-color":"#fff",size:4}},level:{"line-width":2,"line-style":"solid","line-color":"#221F1F"},"line-median-level":{"line-color":"#221F1F","line-width":2},connector:{"line-width":1,"line-style":"solid","line-color":"#29A2CC"}}}},zingchart.bind(null,"dataparse",function(e,o){for(var t=e.theme,l=0,i=o[ZC._[16]].length;l<i;l++)if(o[ZC._[16]][l].type&&("boxplot"===o[ZC._[16]][l].type||"hboxplot"===o[ZC._[16]][l].type)){for(var n=o[ZC._[16]][l].type,a=(o[ZC._[16]][l]["plugin-type"]=n,o[ZC._[16]][l]),p=(ZC._todash_(a),{}),r=(a.options&&(p=a.options[n]||a.options),ZC._todash_(p),a[ZC._[50]]=a[ZC._[50]]||{},a[ZC._[50]][ZC._[5]]=a[ZC._[50]][ZC._[5]]||[],a[ZC._[51]]=a[ZC._[51]]||{},a[ZC._[51]]["min-value"]=null!==ZC._n_(a[ZC._[51]]["min-value"])?a[ZC._[51]]["min-value"]:"auto",a[ZC._[11]]=a[ZC._[11]]||[],a[ZC._[11]][0]=a[ZC._[11]][0]||{},a[ZC._[11]][0][ZC._[5]]=a[ZC._[11]][0][ZC._[5]]||[],a.type="boxplot"===n?"mixed":"hmixed",a.plot=a.plot||{},a.plot.mode="normal",a.tooltip=a.tooltip||{},a.tooltip[ZC._[0]]=a.tooltip[ZC._[0]]||"#29A2CC",a[ZC._[11]]),c=(ZC._todash_(r),0),d="",_=0;_<r.length;_++)if(null===ZC._n_(r[_].type)||"boxplot"===r[_].type){var h=r[_]["data-box"]||[],C=r[_]["data-outlier"]||[],s=r[_].options||{},x=ZC.ZCBoxPlot.getDefaults(c,t),Z={id:"boxplot-bar-"+_,type:"boxplot"===n?"vbar":"hbar",mode:"normal",scales:r[_].scales,options:s};for(d in ZC._cp_(x.box,Z),ZC._cp_(r[_],Z),r[_])0===d.indexOf("data-")&&"data-box"!==d&&"data-outlier"!==d&&(Z[d]=r[_][d]);for(var b=[],g=[],u=[],f=[],y=[],v=[],m=[],A=0,w=h.length;A<w;A++){var B=A,k=0,z=(6===h[A].length&&(B=h[A][0],k=1),h[A][0+k]),E=h[A][1+k],F=h[A][2+k],j=h[A][3+k],k=h[A][4+k];b.push([B,j-E,z-E,k-E,F-E]),g.push(E),u.push(z),f.push(k),y.push(F),m.push(E),v.push(j)}Z[ZC._[5]]=b,Z["extra-values"]=3,Z["offset-values"]=g,Z["data-min"]=u,Z["data-max"]=f,Z["data-median"]=y,Z["data-lower-quartile"]=m,Z["data-upper-quartile"]=v,ZC._cp_(p.box,Z),ZC._cp_(s.box,Z);var P={id:"boxplot-scatter"+_,type:"boxplot"===n?"scatter":"hscatter",mode:"normal",scales:r[_].scales,options:s,values:C};for(d in ZC._cp_(x.outlier,P),ZC._cp_(p.outlier,P),ZC._cp_(s.outlier,P),r[_])0===d.indexOf("data-")&&"data-box"!==d&&"data-outlier"!==d&&(P[d]=r[_][d]);r.push(Z),C.length&&r.push(P),c++}for(_=r.length-1;0<=_;_--)null!==ZC._n_(r[_].type)&&"boxplot"!==r[_].type||r.splice(_,1)}return o}),zingchart.bind(null,"legend_item_click",function(e){var o=zingchart.getLoader(e.id),o=zingchart.getGraph(o,e.graphid).o;if(o["plugin-type"]=o["plugin-type"]||"","boxplot"===o["plugin-type"]||"hboxplot"===o["plugin-type"])for(var t=zingchart.exec(e.id,"getobjectsbyclass",{type:"shape",graphid:e.graphid,cls:"boxplot-line-"+e.plotindex}),l=0,i=t.length;l<i;l++)zingchart.exec(e.id,"updateobject",{type:"shape",graphid:e.graphid,id:t[l],data:{alpha:e.visible?0:1}})}),zingchart.bind(null,"objectsinit",function(e){var o=zingchart.getLoader(e.loader.id),t=zingchart.getGraph(o,e.graphid),l=t.o,i=o.L2;if(l["plugin-type"]=l["plugin-type"]||"","boxplot"===l["plugin-type"]||"hboxplot"===l["plugin-type"]){var n={},a=(l.options&&(n=l.options[l["plugin-type"]]||l.options),ZC._todash_(n),l.shapes||[]);for(Z=a.length-1;0<=Z;Z--)-1!==a[Z]["class"].indexOf("boxplot-line-")&&a.splice(Z,1);var p=[];for(Z=0;Z<t.B0.A3.length;Z++)-1!==t.B0.A3[Z].H7.indexOf("boxplot-bar")&&p.push(Z);for(var r=l[ZC._[11]],c=(ZC._todash_(r),0),d=0;d<p.length;d++){for(var _=p[d],h=zingchart.exec(e.loader.id,"getobjectinfo",{object:"plot",graphid:t.L,plotindex:_}),C=t.BM(h.scales[1]),s=r[_].options||{},x=ZC.ZCBoxPlot.getDefaults(c,i),Z=0;Z<t.B0.A3[_].S.length;Z++){var b,g,u,f,y,v,m,A=zingchart.exec(e.loader.id,"getobjectinfo",{object:"node",graphid:t.L,plotindex:_,nodeindex:Z});A.onviewport&&A.visible&&("boxplot"===l["plugin-type"]?(b=ZC._i_(C.AR(A.xdata.min)),g=ZC._i_(C.AR(A.xdata.median)),u=ZC._i_(C.AR(A.xdata.max))):(f=ZC._i_(C.AR(A.xdata.min)),y=ZC._i_(C.AR(A.xdata.median)),v=ZC._i_(C.AR(A.xdata.max))),m={type:"line",id:"boxplot-line-max-connector-"+_+"-"+Z,"class":"boxplot-line-"+_,flat:!1,points:"boxplot"===l["plugin-type"]?[[A.x+A.width/2,C.AW?A.y+A.height:A.y],[A.x+A.width/2,u]]:[[C.AW?A.x:A.x+A.width,A.y+A.height/2],[v,A.y+A.height/2]]},ZC._cp_(x.connector,m),ZC._cp_(n.line,m),ZC._cp_(n["line-max-connector"],m),ZC._cp_(s["line-max-connector"],m),a.push(m),m={type:"line",id:"boxplot-line-max-level-"+_+"-"+Z,"class":"boxplot-line-"+_,flat:!1,points:"boxplot"===l["plugin-type"]?[[A.x+.25*A.width,u],[A.x+.75*A.width,u]]:[[v,A.y+.25*A.height],[v,A.y+.75*A.height]]},ZC._cp_(x.level,m),ZC._cp_(n.line,m),ZC._cp_(n["line-max-level"],m),ZC._cp_(s["line-max-level"],m),a.push(m),m={type:"line",id:"boxplot-line-min-connector-"+_+"-"+Z,"class":"boxplot-line-"+_,flat:!1,points:"boxplot"===l["plugin-type"]?[[A.x+A.width/2,C.AW?A.y:A.y+A.height],[A.x+A.width/2,b]]:[[C.AW?A.x+A.width:A.x,A.y+A.height/2],[f,A.y+A.height/2]]},ZC._cp_(x.connector,m),ZC._cp_(n.line,m),ZC._cp_(n["line-min-connector"],m),ZC._cp_(s["line-min-connector"],m),a.push(m),m={type:"line",id:"boxplot-line-min-level-"+_+"-"+Z,"class":"boxplot-line-"+_,flat:!1,points:"boxplot"===l["plugin-type"]?[[A.x+.25*A.width,b],[A.x+.75*A.width,b]]:[[f,A.y+.25*A.height],[f,A.y+.75*A.height]]},ZC._cp_(x.level,m),ZC._cp_(n.line,m),ZC._cp_(n["line-min-level"],m),ZC._cp_(s["line-min-level"],m),a.push(m),m={type:"line",id:"boxplot-line-median-level-"+_+"-"+Z,"class":"boxplot-line-"+_,flat:!1,points:"boxplot"===l["plugin-type"]?[[A.x,g],[A.x+A.width-.5,g]]:[[y,A.y],[y,A.y+A.height-.5]]},ZC._cp_(x.level,m),ZC._cp_(x["line-median-level"],m),ZC._cp_(n.line,m),ZC._cp_(n["line-median-level"],m),ZC._cp_(s["line-median-level"],m),a.push(m))}c++}l.shapes=a}});
<wddxPacket version='1.0'>
	<header/>
	<data>
		<array length="269">
			<!--
			Template
			<array length="8">
				<string><ISSUE><string>
				<string><REMEDY><string>
				<string><SEVERITY><string>
				<string><FEATURENAME><string>
				<string><CATEGORY><string>
				<string><ATTRIBUTES><string>
				<string><ATTRVALUES><string>
				<string><VERSION><string>
			</array>
			-->
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfobject tag. The value 'com' for the type attribute has been removed. </string>
				<string>Check the usage of the attribute type of the tag cfobject. </string>
				<string>Error</string>
				<string>CFOBJECT</string>
				<string>TagAttribute</string>
				<string>type</string>
				<string>com</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed the value 'fire_now' for the onmisfire attribute. </string>
				<string>Use firenow value instead of fire_now. </string>
				<string>Error</string>
				<string>cfschedule</string>
				<string>TagAttribute</string>
				<string>onmisfire</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named HTMLEditFormat. </string>
				<string>Remove all usages of HTMLEditFormat. </string>
				<string>Error</string>
				<string>HTMLEditFormat</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named ParameterExists. </string>
				<string>Remove all usages of ParameterExists. </string>
				<string>Error</string>
				<string>ParameterExists</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function SetLocale. The possible value 'Spanish (Mexican)' for the SetLocale argument has been removed. </string>
				<string>Remove all usages of Spanish (Mexican). </string>
				<string>Error</string>
				<string>SetLocale</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named cfmediaplayer. </string>
				<string>Remove all usages of cfmediaplayer. </string>
				<string>Error</string>
				<string>cfmediaplayer</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named ReleaseComObject. </string>
				<string>Remove all usages of ReleaseComObject. </string>
				<string>Error</string>
				<string>ReleaseComObject</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function CreateObject. The value 'com' for the parameter 'type' has been removed. </string>
				<string>Check the usage of the parameter 'type' of the function CreateObject. </string>
				<string>Error</string>
				<string>CREATEOBJECT</string>
				<string>Function</string>
				<string>type</string>
				<string>com</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named StoreSetACL. </string>
				<string>Remove all usages of StoreSetACL. </string>
				<string>Error</string>
				<string>StoreSetACL</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named StoreAddACL. </string>
				<string>Remove all usages of StoreAddACL. </string>
				<string>Error</string>
				<string>StoreAddACL</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfapplet tag. </string>
				<string>Do not use cfapplet tag. </string>
				<string>Error</string>
				<string>CFAPPLET</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cftree tag. </string>
				<string>Do not use cftree tag. </string>
				<string>Error</string>
				<string>CFTREE</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfclient tag. </string>
				<string>Do not use cfclient tag. </string>
				<string>Error</string>
				<string>CFCLIENT</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfclientsettings tag. </string>
				<string>Do not use cfclientsettings tag. </string>
				<string>Error</string>
				<string>CFCLIENTSETTINGS</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfslider tag. The attributes align, bgcolor, bold, font, fontsize, hspace, italic, label, lookandfeel, message, notsupported, onvalidate, scale, textcolor and vspace have been removed. </string>
				<string>Check the usage of the attributes align, bgcolor, bold, font, fontsize, hspace, italic, label, lookandfeel, message, notsupported, onvalidate, scale, textcolor and vspace of the tag cfslider. </string>
				<string>Error</string>
				<string>CFSLIDER</string>
				<string>TagAttribute</string>
				<string>align, bgcolor, bold, font, fontsize, hspace, italic, label, lookandfeel, message, notsupported, onvalidate, scale, textcolor, vspace</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfgrid tag. The attributes align, colheaderalign, colheaders, gridlines, highlighthref, space, notsupported, onvalidate, picturebar, rowheaderalign, rowheaderbold, rowheaderfont, rowheaderfontsize, rowheaderitalic, rowheaders, rowheadertextcolor, sortascendingbutton, sortdescendingbutton, sort, space and vspace have been removed. </string>
					<string>Check the usage of the attributes align, colheaderalign, colheaders, gridlines, highlighthref, space, notsupported, picturebar, rowheaderalign, rowheaderbold, rowheaderfont, rowheaderfontsize, rowheaderitalic, rowheaders, rowheadertextcolor, sortascendingbutton, sortdescendingbutton, sort, space and vspace of the tag cfslider. </string>
				<string>Error</string>
				<string>CFGRID</string>
				<string>TagAttribute</string>
				<string>align, colheaderalign, colheaders, gridlines, highlighthref, space, notsupported, picturebar, rowheaderalign, rowheaderbold, rowheaderfont, rowheaderfontsize, rowheaderitalic, rowheaders, rowheadertextcolor, sortascendingbutton, sortdescendingbutton, sort, space, vspace</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function GetMetricData. The cachepops parameter has been removed.</string>
				<string>Check the usage of system function GetMetricData.</string>
				<string>Info</string>
				<string>GetMetricData</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named GetTemplatePath. </string>
				<string>Use GetBaseTemplatePath method as an alternative.</string>
				<string>Error</string>
				<string>GetTemplatePath</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfthread tag. The value 'terminate' for the attribute 'action' has been removed. </string>
				<string>Check the usage of the attribute action of the tag cfthread. </string>
				<string>Error</string>
				<string>CFTHREAD</string>
				<string>TagAttribute</string>
				<string>action</string>
				<string>terminate</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named ThreadInterrupted. </string>
				<string>Change name of UDF ThreadInterrupted. </string>
				<string>Error</string>
				<string>ThreadInterrupted</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named isThreadInterrupted. </string>
				<string>Change name of UDF isThreadInterrupted. </string>
				<string>Error</string>
				<string>isThreadInterrupted</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named InterruptThread. </string>
				<string>Change name of UDF InterruptThread. </string>
				<string>Error</string>
				<string>InterruptThread</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed a system function named ThreadTerminate. </string>
				<string>Remove all usages of ThreadTerminate. </string>
				<string>Error</string>
				<string>ThreadTerminate</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named StructValueArray. </string>
				<string>Change name of UDF StructValueArray. </string>
				<string>Error</string>
				<string>StructValueArray</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named ListGetDuplicates. </string>
				<string>Change name of UDF ListGetDuplicates. </string>
				<string>Error</string>
				<string>ListGetDuplicates</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function DeserializeJSON. An optional parameter has been added to preserve case sensitive structs. </string>
				<string>Check the usage of system function DeserializeJSON. </string>
				<string>Info</string>
				<string>DeserializeJSON</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function Duplicate. An optional parameter has been added to specify whether child elements also need to be cloned. </string>
				<string>Check the usage of system function Duplicate. </string>
				<string>Info</string>
				<string>Duplicate</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function Javacast. It now supports two-dimensional arrays as well.</string>
				<string>Check the usage of system function Javacast. </string>
				<string>Info</string>
				<string>JAVACAST</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has modified the usage of CFQuery tag. A new attribute cachemaxidletime has been added. </string>
				<string>Check the usage of the attribute cachemaxidletime of the tag CFQuery. </string>
				<string>Info</string>
				<string>CFQUERY</string>
				<string>TagAttribute</string>
				<string>cachemaxidletime</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of CFOauth tag. 4 new attributes have been added - granttype, refreshtoken, providerConfig, client_assertion. </string>
				<string>Check the usage of the attribute granttype of the tag CFOauth. </string>
				<string>Info</string>
				<string>CFOAUTH</string>
				<string>TagAttribute</string>
				<string>granttype,refreshtoken,providerConfig,client_assertion</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has modified the usage of CFCatch tag. It can now accept multiple types separated by '|' character. </string>
				<string>Check the usage of the attribute type of the tag CFCatch. </string>
				<string>Info</string>
				<string>CFCATCH</string>
				<string>TagAttribute</string>
				<string>cachemaxidletimeout</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named XmlClear. </string>
				<string>Change name of UDF XmlClear. </string>
				<string>Error</string>
				<string>XMLCLEAR</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named XmlDeleteAt. </string>
				<string>Change name of UDF XmlDeleteAt. </string>
				<string>Error</string>
				<string>XMLDELETEAT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named XMLHasChild. </string>
				<string>Change name of UDF XMLHasChild. </string>
				<string>Error</string>
				<string>XMLHASCHILD</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			 <array length="8">
				<string>ColdFusion (2025 release) has updated the function Rand with 'SHA1PRNG' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function Rand. </string>
				<string>Info</string>
				<string>RAND</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>CColdFusion (2025 release) has updated the function Randomize with 'SHA1PRNG' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function Randomize. </string>
				<string>Info</string>
				<string>RANDOMIZE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function RandRange with 'SHA1PRNG' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function RandRange. </string>
				<string>Info</string>
				<string>RANDRANGE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function Encrypt with 'AES/CBC/PKCS5Padding' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function encrypt. </string>
				<string>Info</string>
				<string>ENCRYPT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function Hash with 'SHA_256' as the new default algorithm instead of 'CFMX_COMPAT'. Also, an optional parameter has been added to specify output encoding. </string>
				<string>Check the usage of system function hash. </string>
				<string>Info</string>
				<string>HASH</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function EncryptBinary with 'AES/CBC/PKCS5Padding' as the new default algorithm instead of 'CFMX_COMPAT. </string>
				<string>Check the usage of system function encryptBinary. </string>
				<string>Info</string>
				<string>ENCRYPTBINARY</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function Decrypt with 'AES/CBC/PKCS5Padding' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function decrypt. </string>
				<string>Info</string>
				<string>DECRYPT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function DecryptBinary with 'AES/CBC/PKCS5Padding' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function decryptBinary. </string>
				<string>Info</string>
				<string>DECRYPTBINARY</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has updated the function GeneratePBKDFKey with setting 'AES/CBC/PKCS5Padding' as the new default algorithm instead of 'CFMX_COMPAT'. </string>
				<string>Check the usage of system function generatePBKDFKey. </string>
				<string>Info</string>
				<string>GENERATEPBKDFKEY</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named XmlUpdate. </string>
				<string>Change name of UDF XmlUpdate. </string>
				<string>Error</string>
				<string>XMLUPDATE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has added a system function named GetGraphQLClient. </string>
				<string>Change name of UDF GetGraphQLClient. </string>
				<string>Error</string>
				<string>GetGraphQLClient</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has added a system function named GenerateGraphQLModels. </string>
				<string>Change name of UDF GenerateGraphQLModels. </string>
				<string>Error</string>
				<string>GenerateGraphQLModels</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has added a system function named SerializeAvro. </string>
				<string>Change name of UDF SerializeAvro. </string>
				<string>Error</string>
				<string>SerializeAvro</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has added a system function named DeserializeAvro. </string>
				<string>Change name of UDF DeserializeAvro. </string>
				<string>Error</string>
				<string>DeserializeAvro</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has added a system function named SerializeProtobuf. </string>
				<string>Change name of UDF SerializeProtobuf. </string>
				<string>Error</string>
				<string>SerializeProtobuf</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has added a system function named DeserializeProtobuf. </string>
				<string>Change name of UDF DeserializeProtobuf. </string>
				<string>Error</string>
				<string>DeserializeProtobuf</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string> Deprecated. Starting with ColdFusion (2023 release), the wrapText attribute will be ignored.</string>
				<string>Check the usage of the attribute wrapText of the tag CFMailpart. </string>
				<string>Error</string>
				<string>CFMAILPART</string>
				<string>TagAttribute</string>
				<string>wrapText</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfheader tag. The attribute statusText has been removed. </string>
				<string>Check the usage of the attribute archive of the tag cfheader. </string>
				<string>Error</string>
				<string>CFHEADER</string>
				<string>TagAttribute</string>
				<string>statusText</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2023 release) has modified the usage of CFHtmltopdf tag. </string>
				<string>Check the usage of the attribute conformance of the tag CFHtmltopdf. </string>
				<string>Info</string>
				<string>CFHTMLTOPDF</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2023</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ArrayPush. </string>
				<string>Change name of UDF ArrayPush. </string>
				<string>Error</string>
				<string>ArrayPush</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ArrayPop. </string>
				<string>Change name of UDF ArrayPop. </string>
				<string>Error</string>
				<string>ArrayPop</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ArrayShift. </string>
				<string>Change name of UDF ArrayShift. </string>
				<string>Error</string>
				<string>ArrayShift</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ArrayUnshift. </string>
				<string>Change name of UDF ArrayUnshift. </string>
				<string>Error</string>
				<string>ArrayUnshift</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ArrayReduceRight. </string>
				<string>Change name of UDF ArrayReduceRight. </string>
				<string>Error</string>
				<string>ArrayReduceRight</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ListReduceRight. </string>
				<string>Change name of UDF ListReduceRight. </string>
				<string>Error</string>
				<string>ListReduceRight</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetMongoService. </string>
				<string>Change name of UDF GetMongoService. </string>
				<string>Error</string>
				<string>GetMongoService</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named MongoObjectIdNew. </string>
				<string>Change name of UDF MongoObjectIdNew. </string>
				<string>Error</string>
				<string>MongoObjectIdNew</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named MongoRegExpNew. </string>
				<string>Change name of UDF MongoRegExpNew. </string>
				<string>Error</string>
				<string>MongoRegExpNew</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named MongoDBRefNew. </string>
				<string>Change name of UDF MongoDBRefNew. </string>
				<string>Error</string>
				<string>MongoDBRefNew</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named MongoBsonJSNew. </string>
				<string>Change name of UDF MongoBsonJSNew. </string>
				<string>Error</string>
				<string>MongoBsonJSNew</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetMongoBsonMaxKey. </string>
				<string>Change name of UDF GetMongoBsonMaxKey. </string>
				<string>Error</string>
				<string>GetMongoBsonMaxKey</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetMongoBsonMinKey. </string>
				<string>Change name of UDF GetMongoBsonMinKey. </string>
				<string>Error</string>
				<string>GetMongoBsonMinKey</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named SetTimeZone. </string>
				<string>Change name of UDF SetTimeZone. </string>
				<string>Error</string>
				<string>SetTimeZone</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetTimeZone. </string>
				<string>Change name of UDF GetTimeZone. </string>
				<string>Error</string>
				<string>GetTimeZone</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ClearTimeZone. </string>
				<string>Change name of UDF ClearTimeZone. </string>
				<string>Error</string>
				<string>ClearTimeZone</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named Millisecond. </string>
				<string>Change name of UDF Millisecond. </string>
				<string>Error</string>
				<string>Millisecond</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringSort. </string>
				<string>Change name of UDF StringSort. </string>
				<string>Error</string>
				<string>StringSort</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringFilter. </string>
				<string>Change name of UDF StringFilter. </string>
				<string>Error</string>
				<string>StringFilter</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringReduce. </string>
				<string>Change name of UDF StringReduce. </string>
				<string>Error</string>
				<string>StringReduce</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringReduceRight. </string>
				<string>Change name of UDF StringReduceRight. </string>
				<string>Error</string>
				<string>StringReduceRight</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringMap. </string>
				<string>Change name of UDF StringMap. </string>
				<string>Error</string>
				<string>StringMap</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringEach. </string>
				<string>Change name of UDF StringEach. </string>
				<string>Error</string>
				<string>StringEach</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringEvery. </string>
				<string>Change name of UDF StringEvery. </string>
				<string>Error</string>
				<string>StringEvery</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StringSome. </string>
				<string>Change name of UDF StringSome. </string>
				<string>Error</string>
				<string>StringSome</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GenerateSCryptHash. </string>
				<string>Change name of UDF GenerateSCryptHash. </string>
				<string>Error</string>
				<string>GenerateSCryptHash</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named VerifySCryptHash. </string>
				<string>Change name of UDF VerifySCryptHash. </string>
				<string>Error</string>
				<string>VerifySCryptHash</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GenerateBCryptHash. </string>
				<string>Change name of UDF GenerateBCryptHash. </string>
				<string>Error</string>
				<string>GenerateBCryptHash</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named VerifyBCryptHash. </string>
				<string>Change name of UDF VerifyBCryptHash. </string>
				<string>Error</string>
				<string>VerifyBCryptHash</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetCloudService. </string>
				<string>Change name of UDF GetCloudService. </string>
				<string>Error</string>
				<string>GetCloudService</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GenerateSAMLSPMetadata. </string>
				<string>Change name of UDF GenerateSAMLSPMetadata. </string>
				<string>Error</string>
				<string>GenerateSAMLSPMetadata</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetSAMLAuthRequest. </string>
				<string>Change name of UDF GetSAMLAuthRequest. </string>
				<string>Error</string>
				<string>GetSAMLAuthRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetSAMLLogoutRequest. </string>
				<string>Change name of UDF GetSAMLLogoutRequest. </string>
				<string>Error</string>
				<string>GetSAMLLogoutRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named InitSAMLAuthRequest. </string>
				<string>Change name of UDF InitSAMLAuthRequest. </string>
				<string>Error</string>
				<string>InitSAMLAuthRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named InitSAMLLogoutRequest. </string>
				<string>Change name of UDF InitSAMLLogoutRequest. </string>
				<string>Error</string>
				<string>InitSAMLLogoutRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ProcessSAMLResponse. </string>
				<string>Change name of UDF ProcessSAMLResponse. </string>
				<string>Error</string>
				<string>ProcessSAMLResponse</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named ProcessSAMLLogoutRequest. </string>
				<string>Change name of UDF ProcessSAMLLogoutRequest. </string>
				<string>Error</string>
				<string>ProcessSAMLLogoutRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named SendSAMLLogoutResponse. </string>
				<string>Change name of UDF SendSAMLLogoutResponse. </string>
				<string>Error</string>
				<string>SendSAMLLogoutResponse</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named IsSAMLLogoutRequest. </string>
				<string>Change name of UDF IsSAMLLogoutRequest. </string>
				<string>Error</string>
				<string>IsSAMLLogoutRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named IsSAMLLogoutResponse. </string>
				<string>Change name of UDF IsSAMLLogoutResponse. </string>
				<string>Error</string>
				<string>IsSAMLLogoutResponse</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetLambdaContext. </string>
				<string>Change name of UDF GetLambdaContext. </string>
				<string>Error</string>
				<string>GetLambdaContext</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetLambdaEvent. </string>
				<string>Change name of UDF GetLambdaEvent. </string>
				<string>Error</string>
				<string>GetLambdaEvent</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named BeginAWSXraySubsegment. </string>
				<string>Change name of UDF BeginAWSXraySubsegment. </string>
				<string>Error</string>
				<string>BeginAWSXraySubsegment</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named EndAWSXraySubsegment. </string>
				<string>Change name of UDF EndAWSXraySubsegment. </string>
				<string>Error</string>
				<string>EndAWSXraySubsegment</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named AddAWSXrayMetadata. </string>
				<string>Change name of UDF AddAWSXrayMetadata. </string>
				<string>Error</string>
				<string>AddAWSXrayMetadata</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named GetKeyPairFromKeystore. </string>
				<string>Change name of UDF GetKeyPairFromKeystore. </string>
				<string>Error</string>
				<string>GetKeyPairFromKeystore</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has added a system function named StructIsCaseSensitive. </string>
				<string>Change name of UDF StructIsCaseSensitive. </string>
				<string>Error</string>
				<string>StructIsCaseSensitive</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has introduced static scope, static variable would now be reserved for scoping.</string>
				<string>Do not use variable named static, or disable static support within CFC by setting property supportStatic to false</string>
				<string>Error</string>
				<string>STATIC</string>
				<string>Language</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has modified the usage of CFPresentation tag. The usage of the attribute Format has been modified.</string>
				<string>Ensure that format attribute is present and not set to empty value ""</string>
				<string>Info</string>
				<string>CFPresentation</string>
				<string>TagAttribute</string>
				<string>Format</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has modified the behaviour of a system function named QueryFilter. The function no longer mutates the given input query, and returns a new object instead of modifying the given one.</string>
				<string>To preserve the old behaviour, set JVM flag coldfusion.query.filter.mutateinputquery to true.</string>
				<string>Info</string>
				<string>QueryFilter</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion (2018 release) has modified the usage of CFDocument tag. The usage of the attribute FlashPaper has been retired.</string>
				<string>Check the usage of the attributes useWebkit of the tag CFDocument. </string>
				<string>Error</string>
				<string>CFDocument</string>
				<string>TagAttribute</string>
				<string>Format</string>
				<string>FlashPaper</string>
				<string>2018</string>
			</array>
			<array length="8">
				<string>ColdFusion (2018 release) has modified the usage of CFFunction tag. The attributes of the tag have been modified. </string>
				<string>Check the usage of the CFFunction. </string>
				<string>Info</string>
				<string>CFFUNCTION</string>
				<string>TagAttribute</string>
				<string>access</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has removed cftree tag. </string>
				<string>Do not use cftree tag. </string>
				<string>Error</string>
				<string>CFTREE</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfmenu tag. </string>
				<string>Do not use cfmenu tag. </string>
				<string>Error</string>
				<string>CFMENU</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfcalendar tag. </string>
				<string>Do not use cfcalendar tag. </string>
				<string>Error</string>
				<string>CFCALENDAR</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfsprydataset tag. </string>
				<string>Do not use cfsprydataset tag. </string>
				<string>Error</string>
				<string>CFSPRYDATASET</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has deprecated the attribute requesttimeout for the tag cfschedule.</string>
				<string>Check the updated doc of tag cfschedule.</string>
				<string>Error</string>
				<string>CFSCHEDULE</string>
				<string>TagAttribute</string>
				<string>requesttimeout</string>
				<string>N/A</string>
				<string>2018</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed the attribute fieldBoost,docBoost for the tag cfindex.</string>
				<string>Check the updated doc of tag cfindex.</string>
				<string>Error</string>
				<string>CFINDEX</string>
				<string>TagAttribute</string>
				<string>fieldBoost,docBoost</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2018 release) has modified the usage of CFSet tag. Closures are now supported in tag syntax as well. </string>
				<string>Check the usage of the CFSet. </string>
				<string>Info</string>
				<string>CFSET</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>
			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named RunAsync. </string>
				<string>Change name of UDF RunAsync. </string>
				<string>Error</string>
				<string>RUNASYNC</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named StructIsOrdered. </string>
				<string>Change name of UDF StructIsOrdered. </string>
				<string>Error</string>
				<string>STRUCTISORDERED</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>


			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named ArrayFirst. </string>
				<string>Change name of UDF ArrayFirst. </string>
				<string>Error</string>
				<string>ArrayFirst</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named cachegetengineproperties. </string>
				<string>Change name of UDF cachegetengineproperties. </string>
				<string>Error</string>
				<string>cachegetengineproperties</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named ArrayLast. </string>
				<string>Change name of UDF ArrayLast. </string>
				<string>Error</string>
				<string>ArrayLast</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named QueryDeleteRow. </string>
				<string>Change name of UDF QueryDeleteRow. </string>
				<string>Error</string>
				<string>QueryDeleteRow</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named QueryDeleteColumn. </string>
				<string>Change name of UDF QueryDeleteColumn. </string>
				<string>Error</string>
				<string>QueryDeleteColumn</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion (2018 release) has added a system function named onRestRequest. </string>
				<string>Change name of UDF onRestRequest. </string>
				<string>Error</string>
				<string>onRestRequest</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2018</string>
			</array>

			<array length="8">
				<string>ColdFusion 2016 has added a system function named StructToSorted. </string>
				<string>Change name of UDF StructToSorted. </string>
				<string>Error</string>
				<string>STRUCTTOSORTED</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named SpreadsheetGetColumnCount. </string>
				<string>Change name of UDF SpreadsheetGetColumnCount. </string>
				<string>Error</string>
				<string>SPREADSHEETGETCOLUMNCOUNT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named QuerySort. </string>
				<string>Change name of UDF QuerySort. </string>
				<string>Error</string>
				<string>QUERYSORT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named QueryReduce. </string>
				<string>Change name of UDF QueryReduce. </string>
				<string>Error</string>
				<string>QUERYREDUCE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion (2021 release) has modified the usage of system function CreateObject. The value 'corba' for the parameter 'type' has been removed. </string>
				<string>Check the usage of the parameter 'type' of the function CreateObject. </string>
				<string>Error</string>
				<string>CREATEOBJECT</string>
				<string>Function</string>
				<string>type</string>
				<string>corba</string>
				<string>2021</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named QueryMap. </string>
				<string>Change name of UDF QueryMap. </string>
				<string>Error</string>
				<string>QUERYMAP</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named EncodeFor. </string>
				<string>Change name of UDF EncodeFor. </string>
				<string>Error</string>
				<string>ENCODEFOR</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named ArrayContainsNoCase. </string>
				<string>Change name of UDF ArrayContainsNoCase. </string>
				<string>Error</string>
				<string>ARRAYCONTAINSNOCASE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named ArrayDeleteNoCase. </string>
				<string>Change name of UDF ArrayDeleteNoCase. </string>
				<string>Error</string>
				<string>ARRAYDELETENOCASE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named BooleanFormat. </string>
				<string>Change name of UDF BooleanFormat. </string>
				<string>Error</string>
				<string>BOOLEANFORMAT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named ExceptionKeyExists. </string>
				<string>Change name of UDF ExceptionKeyExists. </string>
				<string>Error</string>
				<string>EXCEPTIONKEYEXISTS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named Floor. </string>
				<string>Change name of UDF Floor. </string>
				<string>Error</string>
				<string>FLOOR</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named IsPDFArchive. </string>
				<string>Change name of UDF IsPDFArchive. </string>
				<string>Error</string>
				<string>ISPDFARCHIVE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named QueryEach. </string>
				<string>Change name of UDF QueryEach. </string>
				<string>Error</string>
				<string>QUERYEACH</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named QueryFilter. </string>
				<string>Change name of UDF QueryFilter. </string>
				<string>Error</string>
				<string>QUERYFILTER</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named QueryKeyExists. </string>
				<string>Change name of UDF QueryKeyExists. </string>
				<string>Error</string>
				<string>QUERYKEYEXISTS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named ReplaceListNoCase. </string>
				<string>Change name of UDF ReplaceListNoCase. </string>
				<string>Error</string>
				<string>REPLACELISTNOCASE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has added a system function named ValueArray. </string>
				<string>Change name of UDF ValueArray. </string>
				<string>Error</string>
				<string>VALUEARRAY</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFCache tag. The attributes of the tag have been modified. </string>
				<string>Check the usage of the CFCache. </string>
				<string>Info</string>
				<string>CFCACHE</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed the path attribute. All the collections will be created in the collections directory at the location specified in Solr_Home field in the ColdFusion administrator page</string>
				<string>Do not use the attribute path of the tag CFCollection. </string>
				<string>Error</string>
				<string>CFCOLLECTION</string>
				<string>TagAttribute</string>
				<string>path</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFSpreadSheet tag. </string>
				<string>Check the usage of the attribute autosize of the tag CFSpreadSheet. </string>
				<string>Info</string>
				<string>CFSPREADSHEET</string>
				<string>TagAttribute</string>
				<string>autosize</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFloop tag. </string>
				<string>Check the usage of the tag CFloop. </string>
				<string>Info</string>
				<string>CFLOOP</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFQuery tag. The usage of attributes cachedAfter and scale have been updated and the attributes connectString, DBName, DBServer, provider, providerDSN and sql have been removed. </string>
				<string>Check the usage of the attributes cachedAfter, scale, connectString, DBName, DBServer, provider, providerDSN and sql of the tag CFQuery. </string>
				<string>Error</string>
				<string>CFQUERY</string>
				<string>TagAttribute</string>
				<string>cachedAfter,scale,connectString,DBName,DBServer,provider,providerDSN,sql</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFStoredProc tag. The usage of the attribute cachedAfter has been updated and the attributes connectString, DBName, DBServer, DBType, provider and providerDSN have been removed. </string>
				<string>Check the usage of the attributes cachedAfter, connectString, DBName, DBServer, DBType, provider and providerDSN of the tag CFStoredProc. </string>
				<string>Error</string>
				<string>CFSTOREDPROC</string>
				<string>TagAttribute</string>
				<string>cachedAfter,connectString,DBName,DBServer,DBType,provider,providerDSN</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFDBInfo tag. The attribute datasource is now an optional attribute. </string>
				<string>Check the usage of the attribute datasource of the tag CFDBInfo. </string>
				<string>Info</string>
				<string>CFDBINFO</string>
				<string>TagAttribute</string>
				<string>datasource</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFDocument tag. The attribute useWebkit has been removed. </string>
				<string>Check the usage of the attributes useWebkit of the tag CFDocument. </string>
				<string>Error</string>
				<string>CFDocument</string>
				<string>TagAttribute</string>
				<string>useWebkit</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFPdf tag. </string>
				<string>Check the usage of the tag CFPdf. </string>
				<string>Info</string>
				<string>CFPDF</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 2016 has modified the usage of CFPDFParam tag. The attribute source is now a required attribute. </string>
				<string>Check the usage of the attribute source of the tag CFPDFParam. </string>
				<string>Error</string>
				<string>CFPDFPARAM</string>
				<string>TagAttribute</string>
				<string>source</string>
				<string></string>
				<string>2016</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has modified the usage of cflog tag. The attributes date, thread and time have been removed. </string>
				<string>Check the usage of the attributes date, thread and time of the tag cflog. </string>
				<string>Error</string>
				<string>CFLOG</string>
				<string>TagAttribute</string>
				<string>date,thread,time</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cferror tag. The value 'monitor' for the exception attribute has been removed. </string>
				<string>Check the usage of the attribute exception of the tag cferror. </string>
				<string>Error</string>
				<string>CFERROR</string>
				<string>TagAttribute</string>
				<string>exception</string>
				<string>monitor</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has removed cfservlet tag. </string>
				<string>Do not use cfservlet tag. </string>
				<string>Error</string>
				<string>CFSERVLET</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has removed cfservletparam tag. </string>
				<string>Do not use cfservletparam tag. </string>
				<string>Error</string>
				<string>CFSERVLETPARAM</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfgridupdate tag. The attributes connectString, dbName, dbServer, dbType, provider and providerDSN have been removed. </string>
				<string>Check the usage of the attributes connectString, dbName, dbServer, dbType, provider and providerDSN of the tag cfgridupdate. </string>
				<string>Error</string>
				<string>CFGRIDUPDATE</string>
				<string>TagAttribute</string>
				<string>connectString,dbName,dbServer,dbType,provider,providerDSN</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfinsert tag. The attributes connectString, dbName, dbServer, dbType, provider and providerDSN have been removed. </string>
				<string>Check the usage of the attributes connectString, dbName, dbServer, dbType, provider and providerDSN of the tag cfinsert. </string>
				<string>Error</string>
				<string>CFINSERT</string>
				<string>TagAttribute</string>
				<string>connectString,dbName,dbServer,dbType,provider,providerDSN</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfstoredproc tag. The attributes connectString, dbName, dbServer, dbType, provider and providerDSN have been removed. </string>
				<string>Check the usage of the attributes connectString, dbName, dbServer, dbType, provider and providerDSN of the tag cfstoredproc. </string>
				<string>Error</string>
				<string>CFSTOREDPROC</string>
				<string>TagAttribute</string>
				<string>connectString,dbName,dbServer,dbType,provider,providerDSN</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfupdate tag. The attributes connectString, dbName, dbServer, dbType, provider and providerDSN have been removed. </string>
				<string>Check the usage of the attributes connectString, dbName, dbServer, dbType, provider and providerDSN of the tag cfupdate. </string>
				<string>Error</string>
				<string>CFUPDATE</string>
				<string>TagAttribute</string>
				<string>connectString,dbName,dbServer,dbType,provider,providerDSN</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfquery tag. The attributes connectString, dbName, dbServer, provider, providerDSN and sql have been removed. </string>
				<string>Check the usage of the attributes connectString, dbName, dbServer, provider, providerDSN and sql of the tag cfquery. </string>
				<string>Error</string>
				<string>CFQUERY</string>
				<string>TagAttribute</string>
				<string>connectString,dbName,dbServer,provider,providerDSN,sql</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of dbType attribute of cfquery tag. Some possible values of dbType attribute have been removed. </string>
				<string>Check the usage of the dbType attribute of cfquery tag. </string>
				<string>Error</string>
				<string>CFQUERY</string>
				<string>TagAttribute</string>
				<string>dbType</string>
				<string>dynamic,ODBC,Oracle73,Oracle80,OLEDB,DB2</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfcache tag. The attributes cachedirectory and timeout have been removed. </string>
				<string>Check the usage of the attributes cachedirectory and timeout of the tag cfcache. </string>
				<string>Error</string>
				<string>CFCACHE</string>
				<string>TagAttribute</string>
				<string>cachedirectory,timeout</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cffile tag. Some possible values of attributes attribute have been removed. </string>
				<string>Check the usage of the attributes attribute of the tag cffile. </string>
				<string>Error</string>
				<string>CFFILE</string>
				<string>TagAttribute</string>
				<string>attributes</string>
				<string>system,temporary</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfftp tag. The attribute agentname has been removed. </string>
				<string>Check the usage of the attribute agentname of the tag cfftp. </string>
				<string>Error</string>
				<string>CFFTP</string>
				<string>TagAttribute</string>
				<string>agentname</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has removed cfgraph tag. </string>
				<string>Do not use cfgraph tag. </string>
				<string>Error</string>
				<string>CFGRAPH</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has removed cfgraphdata tag. </string>
				<string>Do not use cfgraphdata tag. </string>
				<string>Error</string>
				<string>CFGRAPHDATA</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfldap tag. The attribute filterFile has been removed. </string>
				<string>Check the usage of the attribute filterFile of the tag cfldap. </string>
				<string>Error</string>
				<string>CFLDAP</string>
				<string>TagAttribute</string>
				<string>filterFile</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfcollection tag. Some possible values of action attribute have been removed. </string>
				<string>Check the usage of the action attribute of the tag cfcollection. </string>
				<string>Error</string>
				<string>CFCOLLECTION</string>
				<string>TagAttribute</string>
				<string>action</string>
				<string>map,repair</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfform tag. The attributes passThrough and enableCAB have been removed. </string>
				<string>Check the usage of the attributes passThrough and enableCAB of the tag cfform. </string>
				<string>Error</string>
				<string>CFFORM</string>
				<string>TagAttribute</string>
				<string>passThrough,enableCAB</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfinput tag. The attribute passThrough has been removed. </string>
				<string>Check the usage of the attribute passThrough of the tag cfinput. </string>
				<string>Error</string>
				<string>CFINPUT</string>
				<string>TagAttribute</string>
				<string>passThrough</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfsearch tag. The attribute external has been removed and attribute language has been deprecated. </string>
				<string>Check the usage of the attributes external of the tag cfsearch. </string>
				<string>Error</string>
				<string>CFSEARCH</string>
				<string>TagAttribute</string>
				<string>external,language</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfselect tag. The attribute passThrough has been removed. </string>
				<string>Check the usage of the attribute passThrough of the tag cfselect. </string>
				<string>Error</string>
				<string>CFSELECT</string>
				<string>TagAttribute</string>
				<string>passThrough</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cfslider tag. The attributes img, imgStyle, grooveColor, refreshLabel, tickmarkimages, tickmarklabels, tickmarkmajor and tickmarkminor have been removed. </string>
				<string>Check the usage of the attributes img, imgStyle, grooveColor, refreshLabel, tickmarkimages, tickmarklabels, tickmarkmajor and tickmarkminor of the tag cfslider. </string>
				<string>Error</string>
				<string>CFSLIDER</string>
				<string>TagAttribute</string>
				<string>img,imgStyle,grooveColor,refreshLabel,tickmarkimages,tickmarklabels,tickmarkmajor,tickmarkminor</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has removed cftextinput tag. </string>
				<string>Do not use cftextinput tag. </string>
				<string>Error</string>
				<string>CFTEXTINPUT</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>

			<array length="8">
				<string>ColdFusion 11 has modified the usage of cflocation tag. If Secure Profile is enabled, default value for addtoken attribute will be false. </string>
				<string>Check the usage of the attribute addtoken of the tag cflocation. </string>
				<string>Info</string>
				<string>CFLocation</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named arrayMap. </string>
				<string>Change name of UDF arrayMap. </string>
				<string>Error</string>
				<string>ARRAYMAP</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named arrayReduce. </string>
				<string>Change name of UDF arrayReduce. </string>
				<string>Error</string>
				<string>ARRAYREDUCE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named deserialize. </string>
				<string>Change name of UDF deserialize. </string>
				<string>Error</string>
				<string>DESERIALIZE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named deserializeXML. </string>
				<string>Change name of UDF deserializeXML. </string>
				<string>Error</string>
				<string>DESERIALIZEXML</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named encodeForDN. </string>
				<string>Change name of UDF encodeForDN. </string>
				<string>Error</string>
				<string>ENCODEFORDN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named encodeForLDAP. </string>
				<string>Change name of UDF encodeForLDAP. </string>
				<string>Error</string>
				<string>ENCODEFORLDAP</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named encodeForXMLAttribute. </string>
				<string>Change name of UDF encodeForXMLAttribute. </string>
				<string>Error</string>
				<string>ENCODEFORXMLATTRIBUTE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named encodeForXPath. </string>
				<string>Change name of UDF encodeForXPath. </string>
				<string>Error</string>
				<string>ENCODEFORXPATH</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named generatePBKDFKey. </string>
				<string>Change name of UDF generatePBKDFKey. </string>
				<string>Error</string>
				<string>GENERATEPBKDFKEY</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named getSafeHTML. </string>
				<string>Change name of UDF getSafeHTML. </string>
				<string>Error</string>
				<string>GETSAFEHTML</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named imageGetMetadata. </string>
				<string>Change name of UDF imageGetMetadata. </string>
				<string>Error</string>
				<string>IMAGEGETMETADATA</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named invalidateOAuthAccessToken. </string>
				<string>Change name of UDF invalidateOAuthAccessToken. </string>
				<string>Error</string>
				<string>INVALIDATEOAUTHACCESSTOKEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named isSafeHTML. </string>
				<string>Change name of UDF isSafeHTML. </string>
				<string>Error</string>
				<string>ISSAFEHTML</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named isValidOAuthAccessToken. </string>
				<string>Change name of UDF isValidOAuthAccessToken. </string>
				<string>Error</string>
				<string>ISVALIDOAUTHACCESSTOKEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named listEach. </string>
				<string>Change name of UDF listEach. </string>
				<string>Error</string>
				<string>LISTEACH</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named listMap. </string>
				<string>Change name of UDF listMap. </string>
				<string>Error</string>
				<string>LISTMAP</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named listReduce. </string>
				<string>Change name of UDF listReduce. </string>
				<string>Error</string>
				<string>LISTREDUCE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named queryExecute. </string>
				<string>Change name of UDF queryExecute. </string>
				<string>Error</string>
				<string>QUERYEXECUTE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named queryGetRow. </string>
				<string>Change name of UDF queryGetRow. </string>
				<string>Error</string>
				<string>QUERYGETROW</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named serializeXML. </string>
				<string>Change name of UDF serializeXML. </string>
				<string>Error</string>
				<string>SERIALIZEXML</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named serialize. </string>
				<string>Change name of UDF serialize. </string>
				<string>Error</string>
				<string>SERIALIZE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named spreadSheetAddAutoFilter. </string>
				<string>Change name of UDF spreadSheetAddAutoFilter. </string>
				<string>Error</string>
				<string>SPREADSHEETADDAUTOFILTER</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named spreadSheetAddPageBreaks. </string>
				<string>Change name of UDF spreadSheetAddPageBreaks. </string>
				<string>Error</string>
				<string>SPREADSHEETADDPAGEBREAKS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of system function GetTimeZoneInfo. </string>
				<string>Check the document for GetTimeZoneInfo for details regarding what new information will now be there in result of the method call.</string>
				<string>Info</string>
				<string>GETTIMEZONEINFO</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named getPropertyString. </string>
				<string>Change name of UDF getPropertyString. </string>
				<string>Error</string>
				<string>GETPROPERTYSTRING</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named setPropertyString. </string>
				<string>Change name of UDF setPropertyString. </string>
				<string>Error</string>
				<string>SETPROPERTYSTRING</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a system function named getPropertyFile. </string>
				<string>Change name of UDF getPropertyFile. </string>
				<string>Error</string>
				<string>GETPROPERTYFILE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named getOauthAccessToken. </string>
				<string>Change name of UDF getOauthAccessToken. </string>
				<string>Error</string>
				<string>GETOAUTHACCESSTOKEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named streamingSpreadsheetNew. </string>
				<string>Change name of UDF streamingSpreadsheetNew. </string>
				<string>Error</string>
				<string>STREAMINGSPREADSHEETNEW</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named StreamingSpreadsheetProcess. </string>
				<string>Change name of UDF StreamingSpreadsheetProcess. </string>
				<string>Error</string>
				<string>STREAMINGSPREADSHEETPROCESS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named StreamingSpreadsheetRead. </string>
				<string>Change name of UDF StreamingSpreadsheetRead. </string>
				<string>Error</string>
				<string>STREAMINGSPREADSHEETREAD</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named StreamingSpreadsheetCleanup. </string>
				<string>Change name of UDF StreamingSpreadsheetCleanup. </string>
				<string>Error</string>
				<string>STREAMINGSPREADSHEETCLEANUP</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named CSVRead. </string>
				<string>Change name of UDF CSVRead. </string>
				<string>Error</string>
				<string>CSVREAD</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named CSVProcess. </string>
				<string>Change name of UDF CSVProcess. </string>
				<string>Error</string>
				<string>CSVPROCESS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named CSVWrite. </string>
				<string>Change name of UDF CSVWrite. </string>
				<string>Error</string>
				<string>CSVWRITE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetClearCellRange. </string>
				<string>Change name of UDF SpreadsheetClearCellRange. </string>
				<string>Error</string>
				<string>SPREADSHEETCLEARCELLRANGE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetClearCell. </string>
				<string>Change name of UDF SpreadsheetClearCell. </string>
				<string>Error</string>
				<string>SPREADSHEETCLEARCELL</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetCellRangeValue. </string>
				<string>Change name of UDF SpreadsheetSetCellRangeValue. </string>
				<string>Error</string>
				<string>SPREADSHEETSETCELLRANGEVALUE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetCellHyperlink. </string>
				<string>Change name of UDF SpreadsheetSetCellHyperlink. </string>
				<string>Error</string>
				<string>SPREADSHEETSETCELLHYPERLINK</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetCellHyperlink. </string>
				<string>Change name of UDF SpreadsheetGetCellHyperlink. </string>
				<string>Error</string>
				<string>SPREADSHEETGETCELLHYPERLINK</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetHyperlinks. </string>
				<string>Change name of UDF SpreadsheetGetHyperlinks. </string>
				<string>Error</string>
				<string>SPREADSHEETGETHYPERLINKS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetRepeatingRows. </string>
				<string>Change name of UDF SpreadsheetSetRepeatingRows. </string>
				<string>Error</string>
				<string>SPREADSHEETSETREPEATINGROWS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetRepeatingColumns. </string>
				<string>Change name of UDF SpreadsheetSetRepeatingColumns. </string>
				<string>Error</string>
				<string>SPREADSHEETSETREPEATINGCOLUMNS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetAddPrintGridlines. </string>
				<string>Change name of UDF SpreadsheetAddPrintGridlines. </string>
				<string>Error</string>
				<string>SPREADSHEETADDPRINTGRIDLINES</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetRemovePrintGridlines. </string>
				<string>Change name of UDF SpreadsheetRemovePrintGridlines. </string>
				<string>Error</string>
				<string>SPREADSHEETREMOVEPRINTGRIDLINES</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetIsRowHidden. </string>
				<string>Change name of UDF SpreadsheetIsRowHidden. </string>
				<string>Error</string>
				<string>SPREADSHEETISROWHIDDEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetIsColumnHidden. </string>
				<string>Change name of UDF SpreadsheetIsColumnHidden. </string>
				<string>Error</string>
				<string>SPREADSHEETISCOLUMNHIDDEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetRowHidden. </string>
				<string>Change name of UDF SpreadsheetSetRowHidden. </string>
				<string>Error</string>
				<string>SPREADSHEETSETROWHIDDEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetColumnHidden. </string>
				<string>Change name of UDF SpreadsheetSetColumnHidden. </string>
				<string>Error</string>
				<string>SPREADSHEETSETCOLUMNHIDDEN</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetColumnWidth. </string>
				<string>Change name of UDF SpreadsheetGetColumnWidth. </string>
				<string>Error</string>
				<string>SPREADSHEETGETCOLUMNWIDTH</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetLastRowNumber. </string>
				<string>Change name of UDF SpreadsheetGetLastRowNumber. </string>
				<string>Error</string>
				<string>SPREADSHEETGETLASTROWNUMBER</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetFitToPage. </string>
				<string>Change name of UDF SpreadsheetSetFitToPage. </string>
				<string>Error</string>
				<string>SPREADSHEETSETFITTOTPAGE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetIsStreamingXMLFormat. </string>
				<string>Change name of UDF SpreadsheetIsStreamingXMLFormat. </string>
				<string>Error</string>
				<string>SPREADSHEETISSTREAMINGXMLFORMAT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetIsXMLFormat. </string>
				<string>Change name of UDF SpreadsheetIsXMLFormat. </string>
				<string>Error</string>
				<string>SPREADSHEETISXMLFORMAT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetIsBinaryFormat. </string>
				<string>Change name of UDF SpreadsheetIsBinaryFormat. </string>
				<string>Error</string>
				<string>SPREADSHEETISBINARYFORMAT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetAddDataValidationRule. </string>
				<string>Change name of UDF SpreadsheetAddDataValidationRule. </string>
				<string>Error</string>
				<string>SPREADSHEETADDDATAVALIDATIONRULE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetForceFormulaRecalculation. </string>
				<string>Change name of UDF SpreadsheetSetForceFormulaRecalculation. </string>
				<string>Error</string>
				<string>SPREADSHEETSETFORCEFORMULARECALCULATION</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetForceFormulaRecalculation. </string>
				<string>Change name of UDF SpreadsheetGetForceFormulaRecalculation. </string>
				<string>Error</string>
				<string>SPREADSHEETGETFORCEFORMULARECALCULATION</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetPrintOrientation. </string>
				<string>Change name of UDF SpreadsheetSetPrintOrientation. </string>
				<string>Error</string>
				<string>SPREADSHEETSETPRINTORIENTATION</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetPrintOrientation. </string>
				<string>Change name of UDF SpreadsheetGetPrintOrientation. </string>
				<string>Error</string>
				<string>SPREADSHEETGETPRINTORIENTATION</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetRenameSheet. </string>
				<string>Change name of UDF SpreadsheetRenameSheet. </string>
				<string>Error</string>
				<string>SPREADSHEETRENAMESHEET</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetRowBreak. </string>
				<string>Change name of UDF SpreadsheetSetRowBreak. </string>
				<string>Error</string>
				<string>SPREADSHEETSETROWBREAK</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetRemoveRowBreak. </string>
				<string>Change name of UDF SpreadsheetRemoveRowBreak. </string>
				<string>Error</string>
				<string>SPREADSHEETREMOVEROWBREAK</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetColumnBreak. </string>
				<string>Change name of UDF SpreadsheetSetColumnBreak. </string>
				<string>Error</string>
				<string>SPREADSHEETSETCOLUMNBREAK</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetRemoveColumnBreak. </string>
				<string>Change name of UDF SpreadsheetRemoveColumnBreak. </string>
				<string>Error</string>
				<string>SPREADSHEETREMOVECOLUMNBREAK</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGroupRows. </string>
				<string>Change name of UDF SpreadsheetGroupRows. </string>
				<string>Error</string>
				<string>SPREADSHEETGROUPROWS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetUngroupRows. </string>
				<string>Change name of UDF SpreadsheetUngroupRows. </string>
				<string>Error</string>
				<string>SPREADSHEETUNGROUPROWS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGroupColumns. </string>
				<string>Change name of UDF SpreadsheetGroupColumns. </string>
				<string>Error</string>
				<string>SPREADSHEETGROUPCOLUMNS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetUngroupColumns. </string>
				<string>Change name of UDF SpreadsheetUngroupColumns. </string>
				<string>Error</string>
				<string>SPREADSHEETUNGROUPCOLUMNS</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetActiveCell. </string>
				<string>Change name of UDF SpreadsheetSetActiveCell. </string>
				<string>Error</string>
				<string>SPREADSHEETSETACTIVECELL</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetActiveCell. </string>
				<string>Change name of UDF SpreadsheetGetActiveCell. </string>
				<string>Error</string>
				<string>SPREADSHEETGETACTIVECELL</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetCellType. </string>
				<string>Change name of UDF SpreadsheetGetCellType. </string>
				<string>Error</string>
				<string>SPREADSHEETGETCELLTYPE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetGetCellFormat. </string>
				<string>Change name of UDF SpreadsheetGetCellFormat. </string>
				<string>Error</string>
				<string>SPREADSHEETGETCELLFORMAT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetHeaderImage. </string>
				<string>Change name of UDF SpreadsheetSetHeaderImage. </string>
				<string>Error</string>
				<string>SPREADSHEETSETHEADERIMAGE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>

			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named SpreadsheetSetFooterImage. </string>
				<string>Change name of UDF SpreadsheetSetFooterImage. </string>
				<string>Error</string>
				<string>SPREADSHEETSETFOOTERIMAGE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named getCSPNonce. </string>
				<string>Change name of UDF getCSPNonce. </string>
				<string>Error</string>
				<string>GETCSPNONCE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has added a system function named getMSGraphServiceClient. </string>
				<string>Change name of UDF getMSGraphServiceClient. </string>
				<string>Error</string>
				<string>GETMSGRAPHSERVICECLIENT</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangeconnection tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangeconnection. </string>
				<string>Info</string>
				<string>CFEXCHANGECONNECTION</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangemail tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangemail. </string>
				<string>Info</string>
				<string>CFEXCHANGEMAIL</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangecalendar tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangecalendar. </string>
				<string>Info</string>
				<string>CFEXCHANGECALENDAR</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangetask tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangetask. </string>
				<string>Info</string>
				<string>CFEXCHANGETASK</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangecontact tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangecontact. </string>
				<string>Info</string>
				<string>CFEXCHANGECONTACT</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangefolder tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangefolder. </string>
				<string>Info</string>
				<string>CFEXCHANGEFOLDER</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfexchangeconversation tag. a new attribute access_token has been added. </string>
				<string>Check the usage of the attribute access_token of the tag cfexchangeconversation. </string>
				<string>Info</string>
				<string>CFEXCHANGECONVERSATION</string>
				<string>TagAttribute</string>
				<string>access_token</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named structMap. </string>
				<string>Change name of UDF structMap. </string>
				<string>Error</string>
				<string>STRUCTMAP</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named structReduce. </string>
				<string>Change name of UDF structReduce. </string>
				<string>Error</string>
				<string>STRUCTREDUCE</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
			<array length="8">
				<string>ColdFusion 11 has added a system function named writeBody. </string>
				<string>Change name of UDF writeBody. </string>
				<string>Error</string>
				<string>WRITEBODY</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>11</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfchart tag. The value 'flash' for the format attribute has been removed. 3 new attributes have been added - showScaleMarkers, markers, theme. </string>
				<string>Check the usage of the attributes format, showScaleMarkers, markers and theme of the tag cfchart. </string>
				<string>Info</string>
				<string>CFCHART</string>
				<string>TagAttribute</string>
				<string>format,showScaleMarkers,markers,theme</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
            <array length="8">
				<string>ColdFusion (2025 release) has added a new tag CFChartset. </string>
				<string>Check the usage of the tag CFChartset. </string>
				<string>Info</string>
				<string>CFCHARTSET</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfchartseries tag. 2 new attributes have been added - rules, dataFormat. </string>
				<string>Check the usage of the attributes rules and dataFormat of the tag cfchartchartseries. </string>
				<string>Info</string>
				<string>CFCHARTSERIES</string>
				<string>TagAttribute</string>
				<string>rules,dataFormat</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfinput tag. The attributes sourceForToolTip, autosuggest, autosuggestBindDelay, autosuggestMinLength, delimiter,
					maxResultsDisplayed, showAutosuggestLoadingIcon, and typeahead have been removed. </string>
				<string>Check the usage of the attributes sourceForToolTip, autosuggest, autosuggestBindDelay, autosuggestMinLength, delimiter,
					maxResultsDisplayed, showAutosuggestLoadingIcon, and typeahead of the tag cfinput. </string>
				<string>Error</string>
				<string>CFINPUT</string>
				<string>TagAttribute</string>
				<string>sourceForToolTip,autosuggest,autosuggestBindDelay,autosuggestMinLength,delimiter,maxResultsDisplayed,showAutosuggestLoadingIcon,typeahead</string>
        		<string>N/A</string>
				<string>2025</string>
       </array>
      <array length="8">
				<string>ColdFusion (2025 release) has removed cftable tag. </string>
				<string>Do not use cftable tag. </string>
				<string>Error</string>
				<string>CFTABLE</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
			<array length="8">
				<string>ColdFusion (2025 release) has removed cfcol tag. </string>
				<string>Do not use cfcol tag. </string>
				<string>Error</string>
				<string>CFCOL</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
      <array length="8">
				<string>ColdFusion (2025 release) has removed the support for sybase driver. </string>
				<string>Do not use sybase driver. </string>
				<string>Error</string>
				<string>setSybase</string>
				<string>Function</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
			</array>
		<array length="8">
				<string>ColdFusion (2025 release) has removed cfformitem tag. </string>
				<string>Do not use cfformitem  tag. </string>
				<string>Error</string>
				<string>cfformitem</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
		</array>
		<array length="8">
				<string>ColdFusion (2025 release) has removed cfformgroup tag. </string>
				<string>Do not use cfformgroup  tag. </string>
				<string>Error</string>
				<string>cfformgroup</string>
				<string>Tag</string>
				<string>N/A</string>
				<string>N/A</string>
				<string>2025</string>
		</array>
		<array length="8">
				<string>ColdFusion (2025 release) has modified the usage of cfform tag. The values 'xml' and 'flash' for the attribute 'format' and the attributes 'accessible', 'archive', 'codeBase', 'height', 'skin', 'timeout' and 'width' have been removed. </string>
				<string>Check the usage of the attributes accessible, archive, codeBase, format, height, skin, timeout and width of the tag cfform. </string>
				<string>Info</string>
				<string>cfform</string>
				<string>TagAttribute</string>
				<string>accessible,archive,codeBase,format,height,skin,timeout,width</string>
       			<string>N/A</string>
				<string>2025</string>
        </array>
     </array>
	</data>
</wddxPacket>

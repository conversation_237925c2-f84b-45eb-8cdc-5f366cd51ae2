function cf_debug_init() {
    if( "cf_main_cf" == self.name || top == self ) {
        setDebugSource();
    }
}

function setDebugSource() {
    if( top.frames["cf_debug_cf"] ) {
        writeToWindow( top.frames["cf_debug_cf"] );
    } else if( cf_debug_cf_win != null && cf_debug_cf_win.document != null ) {
        writeToWindow( cf_debug_cf_win );
    } else {
        var winFeatures = '"width=300,height=300"';
        cf_debug_cf_win = window.open("blank.html","cf_debug_cf_win","width=400,height=400,resizable=yes,scrollbars=yes");
        writeToWindow( cf_debug_cf_win );
    }
}

function writeToWindow( win ) {
    if( document.getElementById ) { // NS6
        // NS6 needs unescape() or else it writes 'showHide%28%27cf_debug_parameters%27,%27img_cf_debug_parameters%27%29;' for methods
        win.document.write(unescape(document.getElementById("cf_debug").innerHTML));
    } else {
       win.document.write(document.all['cf_debug'].innerHTML);
    }
    win.document.close();
    win.focus();
}

function dockedMode( dockedUserPage ) {
    top.location = dockedUserPage;
    if( cf_debug_cf_win ) {
        cf_debug_cf_win.close();
    }
}

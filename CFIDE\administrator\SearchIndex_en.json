[{"Category": "Server Settings", "SubCategory": "Settings", "Contents": "                                                                                                                                                                      Settings            Timeout Requests after seconds           When checked, requests that take longer than the specified time are terminated. This prevents unusually long requests from occupying server resources and impairing the performance of other requests.             Enable Per Application Settings        When checked, per application settings are enabled server-wide.If unchecked, per app settings are disabled server-wide.              Use UUID for cftoken          Configures ColdFusion to use a UUID rather than a random number for client and session variable cftoken values. A UUID guarantees a unique identifier for the token.                   Enable HTTP status codes          Enables ColdFusion to set HTTP error status codes when ColdFusion errors are returned to the browser. ColdFusion sets an error status code of 404 if the template is not found and an error status code of 500 for server errors.                                                                      Enable Whitespace Management                                                                                 Reduces the file size of the pages that ColdFusion returns to the browser by removing many of the        extra spaces, tabs, and carriage returns that ColdFusion might otherwise persist from the CFML source file.                                              Disable CFC Type Check         When checked, UDF arguments of CFC type is not validated. The arguments are treated as type \"ANY\".Use this setting in a production environment only.                Disable access to internal ColdFusion Java components          Disables the ability for CFML code to access and create Java objects that are part of the internal ColdFusion implementation. This prevents an unauthenticated CFML template from reading or modifying administration and configuration information for this server.              Preserve case for Struct keys for Serialization.             Maintains and preserves the case in which keys of a struct have been defined. If not checked keys     will be converted to uppercase.               Prefix serialized JSON with           Protects web services which return JSON data from cross-site scripting attacks by prefixing serialized JSON strings with a custom prefix.                 Maximum Output Buffer size   KB          Maximum output buffer size for each request in KB. If the output size for any request exceeds this limit, it would automatically get flushed. Under such circumstances the response cannot be cached.              Enable In-Memory File System          Enables the In-Memory File System support             Memory Limit for In-Memory Virtual File System     MB          Specify memory limit in MB for In-Memory Virtual File System.                 Memory Limit per Application for In-Memory Virtual File System     MB          Specify per application memory limit in MB for In-Memory Virtual File System.            Check configuration files for changes every      sec           Causes ColdFusion to watch its configuration files and automatically reload them if they change. This is required if you deploy ColdFusion in a Websphere ND vertical cluster, as multiple instances of ColdFusion share the same configuration files. Most installations should not enable this feature.              Enable Global Script Protection          Specify whether to protect Form, URL, CGI, and Cookie scope variables from cross-site scripting attacks.                  Allow Extra Attributes in AttributeCollection          Specify whether ColdFusion tags can pass non-standard attributes in the attributecollection structure.              Disable creation of unnamed applications           Do not allow creation of applications when no application name is specified in Application.cfm or Application.cfc              Allow adding application variables to Servlet Context           Specify if application variables should be added to Servlet Context. If disabled, only unnamed Application data will be added for JSP/Servlet interoperability.              ORM Search Index Directory                 Specify the absolute path to store index files for ORM search.          Default ScriptSrc Directory             Specify the default path (relative to the web root) to the directory containing the cfform.js file.          Google Map API Key            Specify Google Map API license key.                       Component with onServerStart( ) method             Specify the absolute path to a CFC having onServerStart() method, like \"c:\\server.cfc\". Or specify a dot delimited CFC path under webroot, like \"a.b.server\". By default, ColdFusion will look for server.cfc under webroot.               Enable Null Support          Specify if ColdFusion should enable null support. This will mean that null will not be converted to empty strings.                       Use Java As Regex Engine         Specify if the default Java RegEx engine should be used for processing the regular expressions. If not selected, Perl-compatible engine will be used. Changing the regex engine might require recompiling the coldfusion templates.        Default Maximum Thread Count For Parallel Functions     Specify the default maximum number of threads to be used for parallel functions. ColdFusion functions having parallel support use this value as a default when maxThreads argument is not specified.       Allowed file extensions for CFInclude tag        Specify the file extensions as a comma separated list which gets compiled when used in the CFInclude tag.    Blocked file extensions for CFFile uploads         Specify the file extensions as a comma-separated list, which gets blocked during file uploads using CFFile tag.  * (Wildcard) denotes to block all file uploads and :no-extension denotes to block uploads of files having no file extension.      Content Security Policy Nonce     If enabled, the Content-Security-Policy header will be sent along with a random generated nonce.          Application.cfc/Application.cfm lookup order                    Default order           Until webroot           In webroot           Specify the option ColdFusion should use to search for an Application.cfc/Application.cfm if it is not found in the current folder. By default, ColdFusion will search until the system root.         Executor Pool Configuration (Async Framework)               Core Pool Size     Core pool size is the minimum number of worker threads to keep alive.     Maximum Pool Size     Maximum number of threads that can ever be available in the pool.       Keep Alive Time     MilliSeconds  Timeout in milliseconds for idle threads waiting for work. Threads use this timeout when there are more threads than Core Pool Size present in the pool.            Cloud Services Thread Pool Configuration              Core Pool Size     Core pool size is the minimum number of worker threads to keep alive.     Maximum Pool Size     Maximum number of threads that can ever be available in the pool.       Keep Alive Time     MilliSeconds  Timeout in milliseconds for idle threads waiting for work. Threads use this timeout when there are more threads than Core Pool Size present in the pool.            Error Handlers            Missing Template Handler       Specify the relative path to the template to execute when ColdFusion cannot find a requested template.        Site-wide Error Handler       Specify the relative path to a template to execute when ColdFusion encounters errors while processing a request.            ZIP File Settings            Max Unzip Ratio     When using CFZIP to unzip a file, a ratio of 5 would mean that the maximum permissible uncompressed size of files would be 5 times the uncompressed ZIP file. The unzip action will be stopped if the ratio is reached, or the maximum file size limit is reached.              Request Size Limits              Maximum number of POST request parameters     Maximum number of parameters in a POST request sent to the server. ColdFusion rejects requests if the POST parameters exceed the limit you specify.     Maximum size of post data     MB  Limits the amount of data that can be posted to the server in a single request. ColdFusion rejects requests larger than the specified limit.       Request Throttle Threshold     MB   Requests smaller than the specified limit are not handled by the throttle.     Request Throttle Memory     MB   Limits total memory size for the throttle. ColdFusion queues requests if there is not enough total memory available. Any request larger than this limit will not be processed.            API Manager           Allow REST Discovery     Specify whether to allow API Manager to discover REST services published in ColdFusion.                         © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/server_settings.cfm"}, {"Category": "Server Settings", "SubCategory": "Request Tuning", "Contents": "                                                                                                                                                                     Request Tuning         Request Limits          Maximum number of simultaneous Template requests       The number of CFML page requests that can be processed concurrently. Use this setting to increase overall system performance for heavy load applications. Requests beyond the specified limit are queued.     Maximum number of simultaneous Web Service requests       The number of Web Service requests that can be processed concurrently.       Maximum number of simultaneous CFC function requests       The number of ColdFusion Component methods that can be processed concurrently via HTTP. This does not affect invocation of CFC methods from within CFML, only methods requested via an HTTP request.             Tag Limit Settings          Maximum number of simultaneous Report threads        The maximum number of ColdFusion reports that can be processed concurrently.     Maximum number of threads available for CFTHREAD           The maximum number of threads created by CFTHREAD that will be run concurrently. Threads created by CFTHREAD in excess of this are queued. For this change to take effect, you must restart the ColdFusion Server.            Queue Timeout Settings            Timeout requests waiting in queue after   seconds      If a request has waited in the queue for this long, timeout the request. This value should be at least as long as the Request Timeout setting (currently 60 seconds).         Request Queue Timeout Page       Specify a relative path from the web root to an HTML page to send to clients when a template request times out before running,for example /CFIDE/timeout.html. The page you specify cannot contain CFML.If you do not specify a page, clients receive a 500 Request Timeout error when their request does not run.                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/limits.cfm"}, {"Category": "Server Settings", "SubCategory": "Caching", "Contents": "                                                                                                                                  Caching        Maximum number of cached templates     Limits the number of templates cached using template caching. If the cache is set to a small value, ColdFusion might re-process your templates. If your server has a sufficient amount of memory, you can achieve optimum performance by setting this value to the total number of all of your ColdFusion templates. Setting the cache to a high value does not automatically reduce available memory because ColdFusion caches templates incrementally.         Server wide cache engine             Default server wide caching engine to be used.  Switching cache engine will result in loss of old cached data.             EHCache    JCS     Redis      Memcached             Memcached Cache Settings                Specify comma separated IP addresses of memcached servers like localhost:11211,localhost:11212                                 Memcached Servers                                                                                          Specify server level cache properties for Memcached. Changing these settings requires restarting ColdFusion.                                        Max idle time(seconds)                                                            Max life span(seconds)                                                            Max elements                                                            Eternal                               Redis Cache Settings                  Specify Redis server settings for caching. These settings for caching only applies when Redis is chosen as default cache engine at the server level, or you specify Redis as cache engine at the application level. In case of cluster setup you can specify comma delimited IP addresses of hosts.                              Redis Server                                       Redis Server Port                                       Password                                                         Is Cluster                                                        Is SSL Enabled                                                                                                 Specify server level cache properties for Redis. Changing these settings requires restarting ColdFusion.                                     Max idle time(seconds)                                                            Max life span(seconds)                                                            Max elements                                                            Eternal                               JCS Cache Settings                                                                                                       Choose the data source in which you want to create a table to be used by the JCS JDBC auxiliary cache.                                                                Select data source                                                                                                                                                                                            Override table if already exists                                                                                                                 The JDBC disk cache uses a relational database such as MySQL as a persistent store. The cache elements are serialized and written into a BLOB. You can find more details here :  JCS JDBC Disk Cache         Specify server level cache properties for JCS. Changing these settings requires restarting ColdFusion.                                        Max idle time(seconds)                                                            Max life span(seconds)                                                            Max elements                                                            Eternal                                   Trusted cache          When checked, any requested files found to currently reside in the template cache will not be inspected for potential updates. For sites where templates are not updated during the life of the server, this minimizes file system overhead. This setting does not require restarting the server.              Cache template in request            When checked, any requested files will be inspected only once for potential updates within a request. If unchecked,   requested file will be inspected for changes each and everytime when its accessed within the same request. For application where   templates/components are not expected to reflect updates within the same request, this minimizes file system overhead. This setting does not require restarting the server.              Component cache          When checked, component path resolution is cached and not resolved again. This setting does not require restarting the server.                      Save class files          When you select this option, the class files generated by ColdFusion are saved to disk for reuse after the server restarts. Adobe recommends this for production systems.  During development, Adobe recommends that you do not select this option.               Cache web server paths          Caches page paths on embedded & multiple site web server installations, which provides improved performance.               Use internal cache to store queries          When checked, a server level internal cache is used to store cached queries. By default, cached queries are stored in QUERY region supported by currently selected caching engine at server level. You must restart the server for this change to take effect           Maximum number of cached queries     Limits the maximum number of cached queries that the server will maintain. Cached queries allow for retrieval of result sets from memory rather than through a database transaction. Since the queries reside in memory, and query result set sizes differ, there must be some user-imposed limit to the number of queries that are cached. When this value is exceeded, the oldest query is dropped from the cache and is replaced with the specified query. This setting does not apply to Application-specific caching.      Click the button below to clear the template cache. ColdFusion will reload templates into memory the next time they are requested and recompile them if they have been modified.    Clear folder specific template cache  Click Clear Template Cache of Specific Folder to clear the template cache of the selected folder. ColdFusion reloads templates into the memory, next time they are requested and recompiles them if they have been modified.          Select folder                                                Click the button to clear the component cache. ColdFusion will ignore the resolved path for components and try the resolution again.       Click the button to clear the query cache.       Click the button to clear the dynamo cache.                                                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/caching.cfm"}, {"Category": "Server Settings", "SubCategory": "Client Variables", "Contents": "                                                                                                                                          Client Variables        Client variables let you store user information and preferences between sessions.  The Administrator setting is used only if the attribute clientStorage is not specified in tag cfapplication and the variable clientStorage is not set in the Application.cfc.   To add a ColdFusion data source to the list of available client storage mechanisms, select   the data source from the drop-down list, and then click Add.   To set the data source as the default storage mechanism, select the radio button and Click Apply.                           Select a data source to add as Client Store                                                                 cfartgallery                 cfbookclub                 cfcodeexplorer                 cfdocexamples                                                                                          Select default storage mechanism for Client Sessions                                                       Actions                    Storage Name                    Description                                                                                 Cookie                           Client based text file.                                                                                                                    Registry                                           System registry.                                                                   None                                                                                                       Purge Interval                                                                This option controls how often ColdFusion executes a purge operation on your client stores.        If your client stores are configured to be purged, this will be rate at which the operation will be executed.        It defaults to 1 hour 7 minutes and should not be less than every 30 minutes.                                                                  hours           minutes                                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/clientvariables.cfm"}, {"Category": "Server Settings", "SubCategory": "Memory Variables", "Contents": "                                                                                                                              Memory Variables   The application variables expire when you restart the ColdFusion server. The session variables expire when a user’s session ends. Both these types of variables also expire after a timeout period that you specify on this page or in Application.cfc or in the cfapplication tag.      Use J2EE session variables     Enable Application Variables        Enable Session Variables  (when unchecked, CSRF protection is disabled)          Session Storage Settings               The following settings control where ColdFusion server stores the session variables. These settings only apply when option to use J2EE session variables is not enabled.                        Session Storage                              In Memory       Redis                                   Redis Server                                       Redis Server Port                                       Password                                                   Redis Server Timeout (ms)                                                   Is SSL Enabled                                                                             Use redis for CFLogin                                                                                              Note:  You must restart the ColdFusion application server to change the session storage.           Maximum Timeout               These values specify the maximum timeout period that you can use in a cfapplication tag.                             Application Variables                          days                           hours                           minutes                           seconds                         Session Variables                          days                           hours                           minutes                           seconds                         Default Timeout               These values specify the timeout period that ColdFusion uses if you do not specify an application-specific value in the cfapplication tag.                             Application Variables                          days                           hours                           minutes                           seconds                         Session Variables                          days                           hours                           minutes                           seconds                             Session Cookie Settings               The following ColdFusion session cookie properties can be set both at the server level and the application level. Check HTTPOnly to prevent cookie access through scripts.  Check Secure Cookie for cookies to be available only for encrypted(HTTPS) connections.                         Cookie Timeout                                           minutes                          HTTPOnly                                            Secure Cookie                                           Disable updating ColdFusion internal cookies using ColdFusion tags/functions                                           <PERSON>ie Samesite default value                             -       Strict       Lax       None                                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "settings/memoryvariables.cfm"}, {"Category": "Server Settings", "SubCategory": "Mappings", "Contents": "                                                                                                                                     Mappings  ColdFusion mappings let tags access pages that are outside the web root. If you specify a path that starts with the mapping's logical path in these tags, ColdFusion looks for the page using the mapping's directory path.  ColdFusion also uses mappings to find ColdFusion components (CFCs). The cfinvoke and cfobject tags and CreateObject function look for CFCs in the mapped directories.   Note:   These mappings are independent of web server virtual directories.  If you would like to create a virtual directory to access a given directory through a URL, please consult your web server documentation.           Add / Edit ColdFusion Mappings                              Logical Path                                         Directory Path                                                                             Active ColdFusion Mappings                            Actions                   Logical Path                    Directory Path                                         /CFIDE                /Applications/ColdFusion2025/cfusion/wwwroot/CFIDE                                                                                /gateway                /Applications/ColdFusion2025/cfusion/gateway/cfc                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/mappings.cfm"}, {"Category": "Server Settings", "SubCategory": "Mail", "Contents": "                                                                                                                               Mail                                                Mail Server Settings                             Mail Server                                             Specifies the server for    sending SMTP mail messages. You can specify an Internet address (for    example, mail.company.com) or the IP address of the mail server (for example,    127.0.0.1).                                        User name                                                     Password                                                                         If your mail server requires authentication, you can specify a       user name and password to use.                                                     Sign the mail                                                Select this check box to have ColdFusion Sign the Mail.                                                                             Keystore                                                                              \"Location of the Keystore\" - Keystore containing the private key and certificate. The supported type is JKS (java key store) and pkcs12.                                                             KeystorePassword                                               Password of the Keystore.                                         <PERSON><PERSON><PERSON><PERSON>as of the key with which the certificate and private key is stored in Keystore. If it is not specified then the first entry in the Keystore will be picked up.                                             KeyPassword                                                      Password with which the private key is stored. If it is not specified, KeystorePassword will be used as KeyPassword as well.                                                                                                                Verify mail server connection                     Select this check box to have <PERSON><PERSON><PERSON> verify that it can connect to this mail server when you submit this form.                             Server Port                           The default port number for mail servers. The standard SMTP mail server port is 25.                                                       Backup Mail Servers                               Specifies backup servers for sending SMTP mail messages. Use a comma to separate         the names of multiple servers. To specify a port number other than the default,         use a colon (for example, mail.company.com:123). If your mail server requires         authentication different from the default server, you can specify a user name and password in the format                user:<EMAIL> .                                                                                                                                           Maintain connection to mail server               Select     this check box to keep the connection to a mail server open for reuse     after delivering a message (recommended).                              Connection Timeout(in seconds)                       Specifies the time that ColdFusion should wait for a response from the mail server.                                  Enable SSL socket connections to mail server                         Select this check box to enable SSL encryption on the connections to the mail server.                                                                                                                              Enable TLS connection to mail server              Select this check box to enable Transport Level Security (TLS) on the connection to the mail server.                                             Mail Spool Settings                                       Spool Interval(in seconds)                                 Specifies the time the mail spooler waits to process spooled mail.                                          Spool mail messages     for delivery                        Select this check    box to spool messages for delivery by the mail spooler (recommended). When    not selected, the mail spooler attempts to deliver messages during page    processing.                                                    Allow downloading the attachments for undelivered emails.                 Select this check    box to allow downloading attachments for undelivered emails. It is recommended not to enable it in case undelivered email functionality is not being used.                                                                                                                         Mail Logging Settings                                         Error Log Severity                   Debug            Information            Warning            Error                              Select the type of SMTP-related error messages to log.                                      Log all mail messages sent    by ColdFusion             Select this check box    to save the To, From, and Subject fields of messages to a log file.                                                                Mail Charset Setting                             Default CFMail Charset                         Big5         (Traditional Chinese)              GB2312         (Simplified Chinese)              ISO-2022-JP         (Japanese)              ISO-2022-KR         (Korean)              ISO-8859-13         (Latin-7, Baltic)              ISO-8859-1         (Latin-1, West European)              ISO-8859-2         (Latin-2, Central/East European)              ISO-8859-3         (Latin-3, South European)              ISO-8859-4         (Latin-4, North European)              ISO-8859-5         (Cyrillic)              ISO-8859-6         (Arabic)              ISO-8859-7         (Greek)              ISO-8859-8         (Hebrew)              ISO-8859-9         (Latin-5, Turkish)              US-ASCII        UTF-8         (8-Bit Unicode Transformation Format)                                                                                         © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "mail/index.cfm"}, {"Category": "Server Settings", "SubCategory": "Scheduled Tasks", "Contents": "                                                                                                                           Scheduled Tasks         Scheduled tasks can create static web pages from dynamic data sources. You can also schedule tasks to update Solr searches and to create reports.        Pause All Tasks  Resume All Tasks                 Server Level Scheduled Tasks                              No server level tasks have been scheduled.                                           Application Level Scheduled Tasks                              No application level tasks have been scheduled.                             Enable Cluster Setup                                    Currently supported databases for cluster setup are MySQL, Microsoft SQL Server and Oracle                           Currently cluster setup is not enabled                                                                                                    Select Datasource                                                                                                                                         Create Tables for Cluster Setup                                                                Enable this option only for one node in the cluster. Otherwise, the tables are overridden. If you have created tables from one node, in other nodes you need to only select and choose the data source. All nodes will point to the same data source and therefore will be part of the cluster.                                                                                    © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "scheduler/scheduletasks.cfm"}, {"Category": "Server Settings", "SubCategory": "WebSocket", "Contents": "                                                                                                                                WebSocket     Enable WebSocket Service                             Use Proxy                       ColdFusion WebSocket requests will be served by WebSocket proxy module configured with an external web server. Restart ColdFusion for the setting to take effect.                                      Use Built-in WebSocket Server                       ColdFusion WebSocket requests will be served by the built-in ColdFusion WebSocket server. Restart ColdFusion for the setting to take effect.                                             Port                            The port that the WebSocket server listens to for all incoming ColdFusion WebSocket requests. Restart ColdFusion for the setting to take effect.                                             SSL Port                                  The port that the WebSocket server listens to for secure communication (SSL). Restart ColdFusion for the setting to take effect.                              Keystore                       Location of the Keystore. Keystore contains the private key and certificate. The supported type is JKS(Java KeyStore) and pkcs12.                                     KeyStore Password                                     Password used to open the keystore to load public/private key.                                          Max Data Size                      KB                           The maximum size of the data packet sent/received.                                                  Start Flash Policy Server                             Enables Flash fallback if there is no native WebSocket support at the client side.                                                   Enable WebSocket cluster                       Multicast Port                                              The port that the WebSocket Cluster will use to broadcast node up/down events. Restart ColdFusion for the setting to take effect.       Note: This setting is enabled only when JVM flag 'coldfusion.rmi.enable' is set to true in jvm.config file                                                                                                       © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/websocket.cfm"}, {"Category": "Server Settings", "SubCategory": "Charting", "Contents": "                                                                                                                               Charting       Cache type       Memory Cache  Disk Cache   Charts can be cached either in memory or to disk.  In memory caching is faster, but more memory intensive.      Maximum number of cached images       Specifies the maximum number of charts to store in the cache.  When the cache is full and a new chart is generated, the oldest chart in the cache is discarded. Caching charts results in multiple requests for the same chart being much faster, because the chart is generated only once.        Time-to-Live of each chart in seconds       Specifies the time in seconds after which the generated chart will be deleted from local disk. This can be used to prevent the expiring of chart images when there are many charts being generated in a single request.      Disk cache location       When caching to disk, specifies the directory in which to store the generated charts.                                                                    © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/charting.cfm"}, {"Category": "Server Settings", "SubCategory": "Font Management", "Contents": "                                                                                                                                      Font Management         Register New Font(s) with ColdFusion                              New Font/Font Directory                                                          Current System Fonts                                       Font Family                   Font Face                   Postscript Name                   Font Type                    Usable In                   Path                                                   adobe clean han black                  Adobe Clean Han Black                  AdobeCleanHan-Black                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Black.otf                                                                         adobe clean han bold                  Adobe Clean Han Bold                  AdobeCleanHan-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Bold.otf                                                                         adobe clean han extrabold                  Adobe Clean Han ExtraBold                  AdobeCleanHan-ExtraBold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-ExtraBold.otf                                                                         adobe clean han extralight                  Adobe Clean Han ExtraLight                  AdobeCleanHan-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-ExtraLight.otf                                                                         adobe clean han light                  Adobe Clean Han Light                  AdobeCleanHan-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Light.otf                                                                         adobe clean han normal                  Adobe Clean Han Normal                  AdobeCleanHan-Normal                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Normal.otf                                                                         adobe clean han regular                  Adobe Clean Han Regular                  AdobeCleanHan-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeCleanHan-Regular.otf                                                                         adobeclean-bold                  Adobe Clean Bold                  AdobeClean-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Bold.otf                                                                         adobeclean-bold                  AdobeClean-Bold                  AdobeClean-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Bold.otf                                                                         adobeclean-boldit                  Adobe Clean Bold Italic                  AdobeClean-BoldIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldIt.otf                                                                         adobeclean-boldit                  AdobeClean-BoldIt                  AdobeClean-BoldIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldIt.otf                                                                         adobeclean-boldsemicn                  AdobeClean-BoldSemiCn                  AdobeClean-BoldSemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCn.otf                                                                         adobeclean-boldsemicn                  Adobe Clean Bold SemiCondensed                  AdobeClean-BoldSemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCn.otf                                                                         adobeclean-boldsemicnit                  AdobeClean-BoldSemiCnIt                  AdobeClean-BoldSemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCnIt.otf                                                                         adobeclean-boldsemicnit                  Adobe Clean Bold SemiCondensed Italic                  AdobeClean-BoldSemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-BoldSemiCnIt.otf                                                                         adobeclean-it                  Adobe Clean Italic                  AdobeClean-It                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-It.otf                                                                         adobeclean-it                  AdobeClean-It                  AdobeClean-It                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-It.otf                                                                         adobeclean-light                  AdobeClean-Light                  AdobeClean-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Light.otf                                                                         adobeclean-light                  Adobe Clean Light                  AdobeClean-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Light.otf                                                                         adobeclean-lightit                  Adobe Clean Light Italic                  AdobeClean-LightIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-LightIt.otf                                                                         adobeclean-lightit                  AdobeClean-LightIt                  AdobeClean-LightIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-LightIt.otf                                                                         adobeclean-regular                  Adobe Clean                  AdobeClean-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Regular.otf                                                                         adobeclean-regular                  AdobeClean-Regular                  AdobeClean-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-Regular.otf                                                                         adobeclean-semicn                  Adobe Clean SemiCondensed                  AdobeClean-SemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCn.otf                                                                         adobeclean-semicn                  AdobeClean-SemiCn                  AdobeClean-SemiCn                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCn.otf                                                                         adobeclean-semicnit                  Adobe Clean SemiCondensed Italic                  AdobeClean-SemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCnIt.otf                                                                         adobeclean-semicnit                  AdobeClean-SemiCnIt                  AdobeClean-SemiCnIt                  OPENTYPE                  PDF                                                  /Library/Fonts/AdobeClean-SemiCnIt.otf                                                                         apple braille                  Apple Braille                  AppleBraille                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille.ttf                                                                         apple braille                  Apple Braille Outline 8 Dot                  AppleBraille-Outline8Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Outline 8 Dot.ttf                                                                         apple braille                  Apple Braille Pinpoint 8 Dot                  AppleBraille-Pinpoint8Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Pinpoint 8 Dot.ttf                                                                         apple braille                  Apple Braille Pinpoint 6 Dot                  AppleBraille-Pinpoint6Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Pinpoint 6 Dot.ttf                                                                         apple braille                  Apple Braille Outline 6 Dot                  AppleBraille-Outline6Dot                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Braille Outline 6 Dot.ttf                                                                         apple color emoji                  Apple Color Emoji                  AppleColorEmoji                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Color Emoji.ttc,0                                                                         apple symbols                  Apple Symbols                  AppleSymbols                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Apple Symbols.ttf                                                                         arial hebrew                  Arial Hebrew Bold                  ArialHebrew-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/ArialHB.ttc,1                                                                         arial hebrew                  Arial Hebrew Light                  ArialHebrew-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/ArialHB.ttc,2                                                                         arial hebrew                  Arial Hebrew                  ArialHebrew                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/ArialHB.ttc,0                                                                         arial unicode ms                  Arial Unicode MS                  ArialUnicodeMS                  TRUETYPE                  PDF/FlashPaper                                                  /Library/Fonts/Arial Unicode.ttf                                                                         avenir                  Avenir Roman                  Avenir-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,11                                                                         avenir                  Avenir Oblique                  Avenir-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,10                                                                         avenir black                  Avenir Black                  Avenir-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,2                                                                         avenir black oblique                  Avenir Black Oblique                  Avenir-BlackOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,3                                                                         avenir book                  Avenir Book                  Avenir-Book                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,0                                                                         avenir book                  Avenir Book Oblique                  Avenir-BookOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,1                                                                         avenir heavy                  Avenir Heavy                  Avenir-Heavy                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,4                                                                         avenir heavy                  Avenir Heavy Oblique                  Avenir-HeavyOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,5                                                                         avenir light                  Avenir Light Oblique                  Avenir-LightOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,7                                                                         avenir light                  Avenir Light                  Avenir-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,6                                                                         avenir medium                  Avenir Medium                  Avenir-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,8                                                                         avenir medium                  Avenir Medium Oblique                  Avenir-MediumOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir.ttc,9                                                                         avenir next                  Avenir Next Bold Italic                  AvenirNext-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,1                                                                         avenir next                  Avenir Next Bold                  AvenirNext-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,0                                                                         avenir next                  Avenir Next Italic                  AvenirNext-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,4                                                                         avenir next                  Avenir Next Regular                  AvenirNext-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,7                                                                         avenir next condensed                  Avenir Next Condensed Bold                  AvenirNextCondensed-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,0                                                                         avenir next condensed                  Avenir Next Condensed Regular                  AvenirNextCondensed-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,7                                                                         avenir next condensed                  Avenir Next Condensed Bold Italic                  AvenirNextCondensed-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,1                                                                         avenir next condensed                  Avenir Next Condensed Italic                  AvenirNextCondensed-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,4                                                                         avenir next condensed demi bold                  Avenir Next Condensed Demi Bold Italic                  AvenirNextCondensed-DemiBoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,3                                                                         avenir next condensed demi bold                  Avenir Next Condensed Demi Bold                  AvenirNextCondensed-DemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,2                                                                         avenir next condensed heavy                  Avenir Next Condensed Heavy Italic                  AvenirNextCondensed-HeavyItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,9                                                                         avenir next condensed heavy                  Avenir Next Condensed Heavy                  AvenirNextCondensed-Heavy                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,8                                                                         avenir next condensed medium                  Avenir Next Condensed Medium                  AvenirNextCondensed-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,5                                                                         avenir next condensed medium                  Avenir Next Condensed Medium Italic                  AvenirNextCondensed-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,6                                                                         avenir next condensed medium                  Avenir Next Medium Condensed Italic                  AvenirNextCondensed-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,6                                                                         avenir next condensed ultra light                  Avenir Next Condensed Ultra Light                  AvenirNextCondensed-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,10                                                                         avenir next condensed ultra light                  Avenir Next Condensed Ultra Light Italic                  AvenirNextCondensed-UltraLightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next Condensed.ttc,11                                                                         avenir next demi bold                  Avenir Next Demi Bold Italic                  AvenirNext-DemiBoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,3                                                                         avenir next demi bold                  Avenir Next Demi Bold                  AvenirNext-DemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,2                                                                         avenir next heavy                  Avenir Next Heavy Italic                  AvenirNext-HeavyItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,9                                                                         avenir next heavy                  Avenir Next Heavy                  AvenirNext-Heavy                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,8                                                                         avenir next medium                  Avenir Next Medium Italic                  AvenirNext-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,6                                                                         avenir next medium                  Avenir Next Medium                  AvenirNext-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,5                                                                         avenir next ultra light                  AvenirNext-UltraLight                  AvenirNext-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,10                                                                         avenir next ultra light                  Avenir Next Ultra Light                  AvenirNext-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,10                                                                         avenir next ultra light                  Avenir Next Ultra Light Italic                  AvenirNext-UltraLightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Avenir Next.ttc,11                                                                         courier                  Courier Bold Oblique                  Courier-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,3                                                                         courier                  Courier-Oblique                  Courier-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,2                                                                         courier                  Courier                  Courier                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,0                                                                         courier                  Courier Oblique                  Courier-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,2                                                                         courier                  Courier-BoldOblique                  Courier-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,3                                                                         courier                  Courier Bold                  Courier-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,1                                                                         courier                  Courier-Bold                  Courier-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Courier.ttc,1                                                                         geeza pro                  Geeza Pro Regular                  GeezaPro                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/GeezaPro.ttc,0                                                                         geeza pro                  Geeza Pro Bold                  GeezaPro-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/GeezaPro.ttc,1                                                                         geneva                  Geneva                  Geneva                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Geneva.ttf                                                                         heiti sc                  Heiti SC Light                  STHeitiSC-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Light.ttc,1                                                                         heiti sc                  Heiti SC Medium                  STHeitiSC-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Medium.ttc,1                                                                         heiti tc                  Heiti TC Medium                  STHeitiTC-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Medium.ttc,0                                                                         heiti tc                  Heiti TC Light                  STHeitiTC-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/STHeiti Light.ttc,0                                                                         helvetica                  Helvetica Bold                  Helvetica-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,1                                                                         helvetica                  Helvetica Light                  Helvetica-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,4                                                                         helvetica                  Helvetica Bold Oblique                  Helvetica-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,3                                                                         helvetica                  Helvetica Oblique                  Helvetica-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,2                                                                         helvetica                  Helvetica-BoldOblique                  Helvetica-BoldOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,3                                                                         helvetica                  Helvetica-Oblique                  Helvetica-Oblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,2                                                                         helvetica                  Helvetica                  Helvetica                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,0                                                                         helvetica                  Helvetica-Bold                  Helvetica-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,1                                                                         helvetica                  Helvetica Light Oblique                  Helvetica-LightOblique                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Helvetica.ttc,5                                                                         helvetica neue                  Helvetica Neue Bold                  HelveticaNeue-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,1                                                                         helvetica neue                  Helvetica Neue Thin Italic                  HelveticaNeue-ThinItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,13                                                                         helvetica neue                  Helvetica Neue Light                  HelveticaNeue-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,7                                                                         helvetica neue                  Helvetica Neue UltraLight Italic                  HelveticaNeue-UltraLightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,6                                                                         helvetica neue                  Helvetica Neue Italic                  HelveticaNeue-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,2                                                                         helvetica neue                  Helvetica Neue Medium                  HelveticaNeue-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,10                                                                         helvetica neue                  Helvetica Neue Light Italic                  HelveticaNeue-LightItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,8                                                                         helvetica neue                  Helvetica Neue Condensed Bold                  HelveticaNeue-CondensedBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,4                                                                         helvetica neue                  Helvetica Neue Thin                  HelveticaNeue-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,12                                                                         helvetica neue                  Helvetica Neue                  HelveticaNeue                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,0                                                                         helvetica neue                  Helvetica Neue Bold Italic                  HelveticaNeue-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,3                                                                         helvetica neue                  Helvetica Neue Condensed Black                  HelveticaNeue-CondensedBlack                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,9                                                                         helvetica neue                  Helvetica Neue UltraLight                  HelveticaNeue-UltraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,5                                                                         helvetica neue                  Helvetica Neue Medium Italic                  HelveticaNeue-MediumItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/HelveticaNeue.ttc,11                                                                         kozgopr6n-bold                  KozGoPr6N-Bold                  KozGoPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Bold.otf                                                                         kozgopr6n-bold                  Kozuka Gothic Pr6N B                  KozGoPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Bold.otf                                                                         kozgopr6n-extralight                  KozGoPr6N-ExtraLight                  KozGoPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-ExtraLight.otf                                                                         kozgopr6n-extralight                  Kozuka Gothic Pr6N EL                  KozGoPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-ExtraLight.otf                                                                         kozgopr6n-heavy                  KozGoPr6N-Heavy                  KozGoPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Heavy.otf                                                                         kozgopr6n-heavy                  Kozuka Gothic Pr6N H                  KozGoPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Heavy.otf                                                                         kozgopr6n-light                  KozGoPr6N-Light                  KozGoPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Light.otf                                                                         kozgopr6n-light                  Kozuka Gothic Pr6N L                  KozGoPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Light.otf                                                                         kozgopr6n-medium                  Kozuka Gothic Pr6N M                  KozGoPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Medium.otf                                                                         kozgopr6n-medium                  KozGoPr6N-Medium                  KozGoPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Medium.otf                                                                         kozgopr6n-regular                  Kozuka Gothic Pr6N R                  KozGoPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Regular.otf                                                                         kozgopr6n-regular                  KozGoPr6N-Regular                  KozGoPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozGoPr6N-Regular.otf                                                                         kozminpr6n-bold                  KozMinPr6N-Bold                  KozMinPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Bold.otf                                                                         kozminpr6n-bold                  Kozuka Mincho Pr6N B                  KozMinPr6N-Bold                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Bold.otf                                                                         kozminpr6n-extralight                  KozMinPr6N-ExtraLight                  KozMinPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-ExtraLight.otf                                                                         kozminpr6n-extralight                  Kozuka Mincho Pr6N EL                  KozMinPr6N-ExtraLight                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-ExtraLight.otf                                                                         kozminpr6n-heavy                  Kozuka Mincho Pr6N H                  KozMinPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Heavy.otf                                                                         kozminpr6n-heavy                  KozMinPr6N-Heavy                  KozMinPr6N-Heavy                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Heavy.otf                                                                         kozminpr6n-light                  KozMinPr6N-Light                  KozMinPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Light.otf                                                                         kozminpr6n-light                  Kozuka Mincho Pr6N L                  KozMinPr6N-Light                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Light.otf                                                                         kozminpr6n-medium                  Kozuka Mincho Pr6N M                  KozMinPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Medium.otf                                                                         kozminpr6n-medium                  KozMinPr6N-Medium                  KozMinPr6N-Medium                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Medium.otf                                                                         kozminpr6n-regular                  KozMinPr6N-Regular                  KozMinPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Regular.otf                                                                         kozminpr6n-regular                  Kozuka Mincho Pr6N R                  KozMinPr6N-Regular                  OPENTYPE                  PDF                                                  /Library/Fonts/KozMinPr6N-Regular.otf                                                                         lucida grande                  Lucida Grande                  LucidaGrande                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/LucidaGrande.ttc,0                                                                         lucida grande                  Lucida Grande Bold                  LucidaGrande-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/LucidaGrande.ttc,1                                                                         marker felt                  Marker Felt Wide                  MarkerFelt-Wide                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MarkerFelt.ttc,1                                                                         marker felt                  Marker Felt Thin                  MarkerFelt-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MarkerFelt.ttc,0                                                                         menlo                  Menlo Bold Italic                  Menlo-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,3                                                                         menlo                  Menlo Bold                  Menlo-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,1                                                                         menlo                  Menlo Regular                  Menlo-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,0                                                                         menlo                  Menlo Italic                  Menlo-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Menlo.ttc,2                                                                         monaco                  Monaco                  Monaco                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Monaco.ttf                                                                         muktamahee bold                  MuktaMahee Bold                  MuktaMahee-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,6                                                                         muktamahee extrabold                  MuktaMahee ExtraBold                  MuktaMahee-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,5                                                                         muktamahee extralight                  MuktaMahee ExtraLight                  MuktaMahee-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,4                                                                         muktamahee light                  MuktaMahee Light                  MuktaMahee-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,3                                                                         muktamahee medium                  MuktaMahee Medium                  MuktaMahee-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,2                                                                         muktamahee regular                  MuktaMahee Regular                  MuktaMahee-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,0                                                                         muktamahee semibold                  MuktaMahee SemiBold                  MuktaMahee-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/MuktaMahee.ttc,1                                                                         noteworthy                  Noteworthy Light                  Noteworthy-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Noteworthy.ttc,0                                                                         noteworthy                  Noteworthy Bold                  Noteworthy-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Noteworthy.ttc,1                                                                         noto nastaliq urdu                  Noto Nastaliq Urdu                  NotoNastaliqUrdu                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoNastaliq.ttc,0                                                                         noto sans armenian                  Noto Sans Armenian Bold                  NotoSansArmenian-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,1                                                                         noto sans armenian                  Noto Sans Armenian Regular                  NotoSansArmenian-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,6                                                                         noto sans armenian blk                  Noto Sans Armenian Black                  NotoSansArmenian-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,0                                                                         noto sans armenian extbd                  Noto Sans Armenian ExtraBold                  NotoSansArmenian-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,2                                                                         noto sans armenian extlt                  Noto Sans Armenian ExtraLight                  NotoSansArmenian-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,3                                                                         noto sans armenian light                  Noto Sans Armenian Light                  NotoSansArmenian-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,4                                                                         noto sans armenian med                  Noto Sans Armenian Medium                  NotoSansArmenian-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,5                                                                         noto sans armenian sembd                  Noto Sans Armenian SemiBold                  NotoSansArmenian-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,7                                                                         noto sans armenian thin                  Noto Sans Armenian Thin                  NotoSansArmenian-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansArmenian.ttc,8                                                                         noto sans kannada                  Noto Sans Kannada Bold                  NotoSansKannada-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,6                                                                         noto sans kannada                  Noto Sans Kannada Regular                  NotoSansKannada-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,3                                                                         noto sans kannada black                  Noto Sans Kannada Black                  NotoSansKannada-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,8                                                                         noto sans kannada extrabold                  Noto Sans Kannada ExtraBold                  NotoSansKannada-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,7                                                                         noto sans kannada extralight                  Noto Sans Kannada ExtraLight                  NotoSansKannada-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,1                                                                         noto sans kannada light                  Noto Sans Kannada Light                  NotoSansKannada-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,2                                                                         noto sans kannada medium                  Noto Sans Kannada Medium                  NotoSansKannada-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,4                                                                         noto sans kannada semibold                  Noto Sans Kannada SemiBold                  NotoSansKannada-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,5                                                                         noto sans kannada thin                  Noto Sans Kannada Thin                  NotoSansKannada-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansKannada.ttc,0                                                                         noto sans myanmar                  Noto Sans Myanmar Bold                  NotoSansMyanmar-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,1                                                                         noto sans myanmar                  Noto Sans Myanmar Regular                  NotoSansMyanmar-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,6                                                                         noto sans myanmar blk                  Noto Sans Myanmar Black                  NotoSansMyanmar-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,0                                                                         noto sans myanmar extbd                  Noto Sans Myanmar ExtraBold                  NotoSansMyanmar-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,2                                                                         noto sans myanmar extlt                  Noto Sans Myanmar ExtraLight                  NotoSansMyanmar-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,3                                                                         noto sans myanmar light                  Noto Sans Myanmar Light                  NotoSansMyanmar-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,4                                                                         noto sans myanmar med                  Noto Sans Myanmar Medium                  NotoSansMyanmar-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,5                                                                         noto sans myanmar sembd                  Noto Sans Myanmar SemiBold                  NotoSansMyanmar-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,7                                                                         noto sans myanmar thin                  Noto Sans Myanmar Thin                  NotoSansMyanmar-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,8                                                                         noto sans oriya                  Noto Sans Oriya                  NotoSansOriya                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansOriya.ttc,0                                                                         noto sans oriya                  Noto Sans Oriya Bold                  NotoSansOriya-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansOriya.ttc,1                                                                         noto sans zawgyi                  Noto Sans Zawgyi Bold                  NotoSansZawgyi-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,10                                                                         noto sans zawgyi                  Noto Sans Zawgyi Regular                  NotoSansZawgyi-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,15                                                                         noto sans zawgyi blk                  Noto Sans Zawgyi Black                  NotoSansZawgyi-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,9                                                                         noto sans zawgyi extbd                  Noto Sans Zawgyi ExtraBold                  NotoSansZawgyi-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,11                                                                         noto sans zawgyi extlt                  Noto Sans Zawgyi ExtraLight                  NotoSansZawgyi-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,12                                                                         noto sans zawgyi light                  Noto Sans Zawgyi Light                  NotoSansZawgyi-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,13                                                                         noto sans zawgyi med                  Noto Sans Zawgyi Medium                  NotoSansZawgyi-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,14                                                                         noto sans zawgyi sembd                  Noto Sans Zawgyi SemiBold                  NotoSansZawgyi-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,16                                                                         noto sans zawgyi thin                  Noto Sans Zawgyi Thin                  NotoSansZawgyi-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSansMyanmar.ttc,17                                                                         noto serif myanmar                  Noto Serif Myanmar Bold                  NotoSerifMyanmar-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,1                                                                         noto serif myanmar                  Noto Serif Myanmar Regular                  NotoSerifMyanmar-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,6                                                                         noto serif myanmar blk                  Noto Serif Myanmar Black                  NotoSerifMyanmar-Black                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,0                                                                         noto serif myanmar extbd                  Noto Serif Myanmar ExtraBold                  NotoSerifMyanmar-ExtraBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,2                                                                         noto serif myanmar extlt                  Noto Serif Myanmar ExtraLight                  NotoSerifMyanmar-ExtraLight                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,3                                                                         noto serif myanmar light                  Noto Serif Myanmar Light                  NotoSerifMyanmar-Light                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,4                                                                         noto serif myanmar med                  Noto Serif Myanmar Medium                  NotoSerifMyanmar-Medium                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,5                                                                         noto serif myanmar sembd                  Noto Serif Myanmar SemiBold                  NotoSerifMyanmar-SemiBold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,7                                                                         noto serif myanmar thin                  Noto Serif Myanmar Thin                  NotoSerifMyanmar-Thin                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/NotoSerifMyanmar.ttc,8                                                                         optima                  Optima Italic                  Optima-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,2                                                                         optima                  Optima Bold                  Optima-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,1                                                                         optima                  Optima Regular                  Optima-Regular                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,0                                                                         optima                  Optima ExtraBlack                  Optima-ExtraBlack                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,4                                                                         optima                  Optima Bold Italic                  Optima-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Optima.ttc,3                                                                         palatino                  Palatino Bold                  Palatino-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,2                                                                         palatino                  Palatino                  Palatino-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,0                                                                         palatino                  Palatino Bold Italic                  Palatino-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,3                                                                         palatino                  Palatino Italic                  Palatino-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Palatino.ttc,1                                                                         symbol                  Symbol                  Symbol                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/Symbol.ttf                                                                         system font                  System Font                  .SFNS-Regular                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/SFNS.ttf                                                                         system font                  System Font Regular Italic                  .SFNS-RegularItalic                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/SFNSItalic.ttf                                                                         times                  Times Bold Italic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times                  Times-Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times                  Times-BoldItalic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times                  Times Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         times                  Times Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times                  Times Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times                  Times-Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times                  Times-Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         times-roman                  Times Bold Italic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times-roman                  Times-Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times-roman                  Times-BoldItalic                  Times-BoldItalic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,3                                                                         times-roman                  Times Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         times-roman                  Times Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times-roman                  Times Italic                  Times-Italic                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,2                                                                         times-roman                  Times-Roman                  Times-Roman                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,0                                                                         times-roman                  Times-Bold                  Times-Bold                  TRUETYPE-COLLECTION                  PDF/FlashPaper                                                  /System/Library/Fonts/Times.ttc,1                                                                         zapf dingbats                  Zapf Dingbats                  ZapfDingbatsITC                  TRUETYPE                  PDF/FlashPaper                                                  /System/Library/Fonts/ZapfDingbats.ttf                                                                         zapfdingbats                  ZapfDingbats                  ZapfDingbats                  ADOBE-BUILT-IN                  PDF                                                  ZapfDingbats                                                                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/fonts.cfm"}, {"Category": "Server Settings", "SubCategory": "Document", "Contents": "                                                                                                                               Document         Configure local OpenOffice with ColdFusion                                                                  OpenOffice Directory                                                                                           Configure remote OpenOffice with ColdFusion                                                                 Remote Host                                                                                       Remote Port                                                                                                                                          Note:                          1. If you configure OpenOffice for both local and remote hosts, the OpenOffice that is configured with the remote host shall be used.                                                      2. Restart ColdFusion after configuring remote OpenOffice.                                                             © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/office.cfm"}, {"Category": "Server Settings", "SubCategory": "CCS", "Contents": "                                                                                                                                      CCS     CCS Client                            Current environment                           Specify the deployment type. Each environment will have it's own settings                                       development              production              beta                              Secret Key                      This is the secret key which you need to provide while registering this node with central config server.                    cefb0fb8-a726-4495-9850-127d7e5bce4a                 CCS Cluster Name                      Specify the name of the cluster this node belongs to.                                       CCS Protocol                      Protocol of central config server                                http          https                         CCS Server                      IP address of central config server                                      CCS Port                         Port at which central config server is running                                      CCS Enabled                      Is central config server enabled or disabled for this node                             Enabled          Disabled                       Enable and Push                                 Status                      Is central config server running or not                       Not Running         Load settings from:                         Environment                            Deployment type from where you want to load settings for this node                                       development              production              beta                                       Version                            Specific version of above specified deployment whose settings you want to load for this node.                                    Latest Version                                                             Version History:    There are no versions created.                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "settings/ccs.cfm"}, {"Category": "Server Settings", "SubCategory": "Java and JVM", "Contents": "                                                                                                                               Java and JVM     Java and JVM settings control the way ColdFusion starts the Java Virtual Machine when it starts.  You can control settings like what classpaths are used and how memory is allocated as well as add custom command line arguments.  Changing these settings requires restarting ColdFusion.  If you enter an incorrect setting, ColdFusion may not restart properly.  When you select the Submit button, backups of the jvm.config file are created. These backups can be used to restore the file in case of significant changes.  You can locate the jvm.config file at /Applications/ColdFusion2025/cfusion/bin/jvm.config      Java Virtual Machine Path         Specifies the location of the Java Virtual Machine.      Minimum JVM Heap Size (in MB)          Maximum JVM Heap Size (in MB)          The Memory Size settings determine the amount of memory that the JVM can use for programs and data.      ColdFusion Class Path     {application.home}/lib/updates,{application.home}/lib/,{application.home}/gateway/lib/,{application.home}/wwwroot/WEB-INF/cfform/jars,{application.home}/bin/cf-osgicli.jar    Specifies any additional class paths for the JVM, with multiple directories separated by  commas.      JVM Arguments     -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005 --add-exports=java.desktop/sun.awt.image=ALL-UNNAMED --add-exports=java.desktop/sun.java2d=ALL-UNNAMED --add-exports=java.base/sun.security.tools.keytool=ALL-UNNAMED --add-exports=java.base/sun.util.calendar=ALL-UNNAMED --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-opens=java.desktop/java.awt=ALL-UNNAMED --add-opens=java.desktop/java.awt.image=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED --add-opens=java.base/java.lang.ref=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.security.rsa=ALL-UNNAMED --add-opens=java.base/sun.security.pkcs=ALL-UNNAMED --add-opens=java.base/sun.security.x509=ALL-UNNAMED --add-opens=java.base/sun.security.util=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/sun.util.cldr=ALL-UNNAMED --add-opens=java.base/sun.util.locale.provider=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED -XX:+UseParallelGC -Djdk.attach.allowAttachSelf=true -Dcoldfusion.home={application.home} -Duser.language=en -Dcoldfusion.rootDir={application.home} -Dcoldfusion.libPath={application.home}/lib -Dorg.apache.coyote.USE_CUSTOM_STATUS_MSG_IN_HEADER=true -Dcoldfusion.jsafe.defaultalgo=FIPS186Random -Dorg.eclipse.jetty.util.log.class=org.eclipse.jetty.util.log.JavaUtilLog -Djava.util.logging.config.file={application.home}/lib/logging.properties -Dtika.config=tika-config.xml -Djava.locale.providers=COMPAT,SPI -Dsun.font.layoutengine=icu -Dcom.sun.media.jai.disableMediaLib=true -Djdk.lang.Process.allowAmbiguousCommands=true -Dcoldfusion.datasource.blocked.properties=allowLoadLocalInfile,autoDeserialize    Specifies any specific JVM initialization options, separated by spaces.                                                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "settings/jvm.cfm"}, {"Category": "Server Settings", "SubCategory": "SSL Certificate", "Contents": "                                                                                                                                                                                                   Configure SSL Certificate                 Use SSL Certificate                        Specify an existing SSL certificate or create new one.                            Existing          Create                            Certificate location*                        Specify the file path where the SSL certificate is stored.                                                      Password*                        Enter the password for accessing the SSL certificate.                                               Algorithm                        Select the encryption algorithm for the SSL certificate (e.g., RSA).                                         UNDEFINED                  RSA                  DSA                  EC                                     Https                        Select to allow HTTPS protocol for secure, encrypted communication.                               Enable                        Http                        Select to allow HTTP protocol for standard, unencrypted communication.                               Enable                                            Distinguished Name*                      A unique identifier for an entity, containing attributes like Common Name (CN), Organization (O), and Location (L). For example, CN=localhost,O=Organization,OU=OrgUnit,L=Location,ST=State,C=Country                                          Password*                      Enter the password for accessing the SSL certificate.                                          Certificate Name*                      Enter the name of the SSL certificate. The name must be unique.                                                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "settings/sslcertificate.cfm"}, {"Category": "Server Settings", "SubCategory": "Settings Summary", "Contents": "                                                                                                                                    When clicked, generates a PDF with the Server Settings in a new Window.                                                                             Settings Summary   Report generated on 31 Jan, 2025 02:54 PM  This report shows the status of all ColdFusion configuration settings.To display the area of the ColdFusion Administrator where you can edit the group settings,click any of the groups in the report.         System Information                        Server Details                   Server Product            ColdFusion 2025                  Version            2025,0,0,331320                  Edition            Developer                      Operating System            Mac OS X                    OS Version            15.1.1                      Adobe Driver Version            5.1.4 (Build 0001)                         Tomcat Version               10.1.34.0                        Device ID            29c8c3c0d5146c294ef6b2b9615fa0f0e5f419a3a8142b71d361215b5ae9e738                             Package Details                       Installed package(s)            adminapi[2025.0.0.331320]  administrator[2025.0.0.331320]  ajax[2025.0.0.331320]  awsdynamodb[2025.0.0.331320]  awslambda[2025.0.0.331320]  awss3[2025.0.0.331320]  awss3legacy[2025.0.0.331320]  awssns[2025.0.0.331320]  awssqs[2025.0.0.331320]  axis[2025.0.0.331320]  azureblob[2025.0.0.331320]  azureservicebus[2025.0.0.331320]  caching[2025.0.0.331320]  ccs[2025.0.0.331320]  cfmongodb[2025.0.0.331320]  chart[2025.0.0.331320]  db2[2025.0.0.331320]  debugger[2025.0.0.331320]  derby[2025.0.0.331320]  document[2025.0.0.331320]  dotnet[2025.0.0.331320]  eventgateways[2025.0.0.331320]  exchange[2025.0.0.331320]  feed[2025.0.0.331320]  ftp[2025.0.0.331320]  gcpfirestore[2025.0.0.331320]  gcppubsub[2025.0.0.331320]  gcpstorage[2025.0.0.331320]  graphqlclient[2025.0.0.331320]  htmltopdf[2025.0.0.331320]  image[2025.0.0.331320]  mail[2025.0.0.331320]  msgraph[2025.0.0.331320]  mysql[2025.0.0.331320]  oracle[2025.0.0.331320]  orm[2025.0.0.331320]  ormsearch[2025.0.0.331320]  pdf[2025.0.0.331320]  pmtagent[2025.0.0.331320]  postgresql[2025.0.0.331320]  presentation[2025.0.0.331320]  print[2025.0.0.331320]  redissessionstorage[2025.0.0.331320]  report[2025.0.0.331320]  saml[2025.0.0.331320]  scheduler[2025.0.0.331320]  search[2025.0.0.331320]  sharepoint[2025.0.0.331320]  spreadsheet[2025.0.0.331320]  sqlserver[2025.0.0.331320]  websocket[2025.0.0.331320]  zip[2025.0.0.331320]                             JVM Details                       Java Version            21.0.6                    Java Vendor            Oracle Corporation                    Java Vendor URL             https://java.oracle.com/                     Java Home            /Applications/ColdFusion2025/jre/Contents/Home                    Java File Encoding            UTF8                    Java Default Locale            en_IN                    File Separator            /                    Path Separator            :                    Line Separator            Chr(10)                    User Name            root                    User Home            /var/root                    User Dir            /Applications/ColdFusion2025/cfusion/bin                    Java VM Specification Version            21                    Java VM Specification Vendor            Oracle Corporation                    Java VM Specification Name            Java Virtual Machine Specification                    Java VM Version            21.0.6+8-LTS-188                    Java VM Vendor            Oracle Corporation                    Java VM Name            Java HotSpot(TM) 64-Bit Server VM                    Java Specification Version            21                    Java Specification Vendor            Oracle Corporation                    Java Specification Name            Java Platform API Specification                    Java Class Version            65.0                      CF Server Java Class Path            :/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/fluent-hc-4.5.13.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-datatype-jsr310.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-jetty-http-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpclient-cache.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-xjc-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-discovery-0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jutf7-0.9.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/msg-simple-1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/slf4j-reload4j.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/mail.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/certjWithNative.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/json.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cxf-rt-rs-client.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-text-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta-oro-2.0.6.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/hk2-locator-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xsdlib.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/protobuf-java-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-csv.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.mail-api-2.1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-codec.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-server-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ehcache.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ehcache-web-2.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jsr107cache.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/batik-ext.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-pkg-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-servlet-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/serializer.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.activation-2.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-httpclient-3.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-xmp-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-api-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpcore-nio-4.4.14.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bcprov-jdk15on-153.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/activation-1.1.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-impl-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jopt-simple-5.0.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jboss-logging-3.3.0.Final.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/Saxon-HE-9.8.0-10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.inject-api-2.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/closure-compiler.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-configuration-1.10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/avro.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-simple-http-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-zip-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-libs-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xalan.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-xml-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jline-3.13.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-image-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-jakarta-rs-json-provider-2.15.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/json-patch-master-1.10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xml-apis-ext.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-media-jaxb-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/classmate-1.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/slf4j-api.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/js.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpclient.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-audiovideo-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-jdk-http-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-collections4-4.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/management-api-1.1-rev-1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/joda-time-2.8.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ant.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/icu4j-charsetdetect-52_2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-advancedmedia-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parsers-standard-package.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-io.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-api.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jempbox-1.8.16.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-html-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-font-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/batik-css.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-logging.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-mail-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion-req.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/batik-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/log4j.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-ocr-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/esapi.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpcore.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/asm-all-5.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/relaxngDatatype.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-databind.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cxf-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/common-io.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-net.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/restfb.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion-ua.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/btf-1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-guava-2.26-b03.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/protobuf-java.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-nlp-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxb-core-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-module-jaxb-annotations.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/objenesis-2.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/picocli-4.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-webarchive-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-sqlite3-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.annotation-api-1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/wsdl4j-1.6.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-beanutils.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/geronimo-stax-api_1.0_spec-1.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/credential-secure-storage-1.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/neko-htmlunit.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/spring-security-crypto-5.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/java-xmlbuilder.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-pool-1.6.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/threaddump.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tagsoup-1.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-mail-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.ws.rs-api-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-pool2-2.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jna-5.14.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/picocli-shell-jline3-4.0.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-entity-filtering-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-dbcp-1.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/log4j-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/certj.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jboss-transaction-api_1.2_spec-1.0.1.Final.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxws-api-2.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/failureaccess.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.xml.soap-api-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-miscoffice-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/dom4j-2.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/namespace.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-coreutils-1.8.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-jdbc-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-code-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/oshi-properties.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/maven-invoker-3.2.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/gson.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bcel-5.1-jnbridge.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-common-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxws-api-jakarta-2.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-dataformat-cbor.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javassist-3.20.0-GA.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-annotations.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/clibwrapper_jiio.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/ant-launcher.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.json.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tt-bytecode.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/apache-mime4j-core-0.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/felix.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jandex-2.0.3.Final.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jax-qname-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-lang3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/icu4j-52_2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-compress.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xml-apis.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bcel.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-hk2-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.xml.soap-api-3.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xercesImpl.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-news-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jaxrpc-jakarta.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/antisamy.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/imageio-metadata.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/maven-shared-utils-3.3.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpasyncclient-4.1.4.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-digest-commons.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cfusion-osgi.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/xmpcore.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.validation-api-3.0.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-jakarta-rs-base-2.15.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/swagger4j-1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta.annotation-api-2.1.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.ws.rs-api-2.0.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-container-servlet-core-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/apache-mime4j-dom-0.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/imageio-jpeg.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jakarta-slide-webdavlib-2.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/mlibwrapper_jai.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/javax.inject-2.4.0-b34.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/wsproxyconfig.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/hk2-utils-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/cxf-rt-frontend-jaxrs.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-pdf-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/imageio-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jdom2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/antlr-2.7.7.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-dataformat-smile.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-media-json-jackson-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-microsoft-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jose4j.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-crypto-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-scientific-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-cad-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/log4j-api.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jna-platform-5.14.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-dataformat-yaml.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jersey-client-3.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jnbcore.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/saaj.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jansi.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jcifs-1.3.15.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/guava.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/pdfencryption.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/tika-parser-apple-module.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/commons-vfs2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/httpmime-4.5.13.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/hk2-api-3.1.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/jackson-core.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/common-lang.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/gateway/lib/examples.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/gateway/lib: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-ext.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-css.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/commons-discovery.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/commons-logging.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/concurrent.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/jnet.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/jcert.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-transcoder.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars/batik-awt-util.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/wwwroot/WEB-INF/cfform/jars: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/bin/cf-osgicli.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis-jakarta-1.2.1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/javac-shaded-9+181-r4173-1.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis-2025.0.0.331320.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/xmlschema-core-2.3.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axiom-dom-jakarta-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/woden-core-1.0M10.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-kernel-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/commons-fileupload-1.5.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/wsdl4j-1.6.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axiom-api-jakarta-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axiom-impl-jakarta-1.4.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-adb-codegen-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-transport-http-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-transport-local-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/google-java-format-1.7.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/geronimo-ws-metadata_2.0_spec-1.1.3.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/wstx-asl-3.2.9.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/neethi-3.2.0.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-codegen-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-jaxws-jakarta-1.8.2.jar: &nbsp:/Applications/ColdFusion2025/cfusion/bin/Applications/ColdFusion2025/cfusion/lib/bundleaxis/axis2-adb-jakarta-1.8.2.jar: &nbsp:                  Java Class Path            /Applications/ColdFusion2025/cfusion/runtime/bin/tomcat-juli.jar &nbsp: /Applications/ColdFusion2025/cfusion/bin/cf-bootstrap.jar &nbsp: /Applications/ColdFusion2025/cfusion/bin &nbsp                    Java Ext Dirs                                   Printer Details        Default Printer         10.193.64.21        Printers      10.193.64.21                      Server Information                         General Settings                    Timeout requests            Yes                    Enable Per App Settings            Yes                    Enable Null Support            No                          Use Java As Regex Engine            No                      Request Time Limit            60 seconds                   Use UUID for CFToken            Yes                    Enable Whitespace Management            Yes                    Disable Service Factory            No                    Protect serialized JSON            No                    Protect Serialized JSON Prefix            //                    Missing Template Handler                                Site-wide Error Handler                                Enable HTTP status codes            Yes                    Enable Global Script Protection            Yes                    ORMSearch Index Directory                                Default CFForm ScriptSrc Directory            /cf_scripts/scripts/                    Google Map Key                                 Allowed file extensions for CFInclude tag            *                    Maximum size of post data            20 MB                   Request Throttle Threshold            4 MB                   Request Throttle Memory             200 MB                      Executor Pool Configuration (Async Framework)                     Core Pool Size            25                    Max Pool Size            50                     Keep Alive Time            2000 milliseconds                         Request Tuning                    Simultaneous request limit            25                      Web Service request limit            5                    CFC request limit            15                      CFThread Pool Size            10                    Maximum number of report threads            8                      Request Queue Timeout            60 seconds                   Request Queue Timeout Page                                      Caching                    Template cache size            1024 templates                   Enable trusted cache            No                    Cached query limit            100                  Save Class Files            Yes                    Caching engine                Ehcache                        JCS DSN name                                Is JCS cluster enabled            No                    Memcached servers                                Redis server                                Redis port            0                    Redis cluster enabled            No                    Custom caches                                        Client Variable Settings                    Default client variable store            Cookie                      Purge Interval            1 hours 7 minutes                         Client Stores                      Cookie                     Type            COOKIE                    Description            Client based text file.                        Purge data after time limit            Yes                    Time limit            10 days                   Disable global updates            No                       Registry                     Type            REGISTRY                    Description            System registry.                        Purge data after time limit            Yes                    Time limit            90 days                   Disable global updates            No                          Memory Variables                      J2EE Sessions            No                         Application Variables                   Enable Application Variables            Yes                    Default Timeout            2,0,0,0                    Maximum Timeout            2,0,0,0                       Session Variables                Enable session variables            Yes                    Default Timeout            0,0,20,0                    Maximum Timeout            2,0,0,0                        ColdFusion Mappings                      /gateway              /Applications/ColdFusion2025/cfusion/gateway/cfc                      /CFIDE              /Applications/ColdFusion2025/cfusion/wwwroot/CFIDE                        Mail Connection Settings                    Default Server Port            25                    Connection Timeout            60 seconds                   Spool Interval            15 seconds                  Mail Delivery Threads            10                    Maintain Connection to Mail Server            No                      Spool Messages To            disk                    Max Messages Spooled to Memory            50000                    Default CFMail Charset            UTF-8                    Use SSL Connection            No                    Use TLS            No                     Default Mail Server                         Mail Logging Settings                        Log Severity               warning                         Log all E-mail messages sent by ColdFusion               No                          Charting                    Cache Type            disk images                   Maximum number of images in cache            50 images                   Maximum number of charting threads            1                    Disk cache location            /Applications/ColdFusion2025/cfusion/tmpCache/CFFileServlet/_cf_chart                           Data & Services                         Database Data Sources                         cfartgallery                     CF data source name            cfartgallery                    Description                                Driver            Apache Derby Embedded                    JDBC URL            **************************************************************************                    Username                                Login timeout            30 seconds                   Long text buffer size            64000                    Timeout            1200 seconds                   Maintain connections            Yes                    Interval            420 seconds                   Restricted SQL operations                                Disable connections            No                         cfbookclub                     CF data source name            cfbookclub                    Description                                Driver            Apache Derby Embedded                    JDBC URL            ************************************************************************                    Username                                Login timeout            30 seconds                   Long text buffer size            64000                    Timeout            1200 seconds                   Maintain connections            Yes                    Interval            420 seconds                   Restricted SQL operations                                Disable connections            No                         cfdocexamples                     CF data source name            cfdocexamples                    Description                                Driver            Apache Derby Embedded                    JDBC URL            *****************************************************************************                    Username                                Login timeout            30 seconds                   Long text buffer size            64000                    Timeout            1200 seconds                   Maintain connections            Yes                    Interval            420 seconds                   Restricted SQL operations                                Disable connections            No                         cfcodeexplorer                     CF data source name            cfcodeexplorer                    Description                                Driver            Apache Derby Embedded                    JDBC URL            ******************************************************************************                    Username                                Login timeout            30 seconds                   Long text buffer size            64000                    Timeout            1200 seconds                   Maintain connections            Yes                    Interval            420 seconds                   Restricted SQL operations                                Disable connections            No                          Web Services                        PDF Service Managers                               localhost                          Host Name               localhost                         Port               8997                         Weight               2                         Https Enabled               No                         Service Manager Enabled               No                         Remote Service Manager               No                               Debugging & Logging                         Debugging Settings                    Enable debugging            No                    Enable Robust Exception Information            No                    Display format            classic.cfm                    Execution times            Yes                    Execution time format            summary                    Execution time highlight threshold            250 ms                  Database activity            Yes                    Exception information            Yes                    Tracing information            Yes                    Timer Information            No                    Variables            Yes                       Variables                   Application            No                    CGI            Yes                    Client            Yes                    Cookie            Yes                    Form            Yes                    Request            No                    Server            No                    Session            Yes                    URL            Yes                        Debugging IP Addresses                    Debugging IP Address Restrictions            127.0.0.1 0:0:0:0:0:0:0:1 ::1                        Line Debugger Settings                    Allow Line Debugging            NO                    Debugger Port            5005                    Max Simultaneous Debugging Sessions            5                        Logging Settings                    Log directory            /Applications/ColdFusion2025/cfusion/logs                    Maximum file size            5000 KB                  Maximum number of archives            10                      Log slow pages            No                    Slow page time limit            30 seconds                   Log CORBA calls            No                               Log scheduled tasks            No                             Schedule Tasks & Probes                         Scheduled Tasks                        System Probes                      Probe Settings                    Notification Email Recipients                                Notification Sent From            ColdFusionProbe@localhost                    Probe.cfm URL                                Probe.cfm Username                                      System Probes                             Extensions                        CFX Tags                          Custom Tag Paths                      /Applications/ColdFusion2025/cfusion/CustomTags                          CORBA                    Selected connector                 [none]                            Connectors                            Event Gateways                         Settings                    Enable Event Gateway            No                    Thread Pool Size            1                    Max Queue Size            10                      Gateway Types                       SMS                     Description            Handles SMS text messaging                    Class            coldfusion.eventgateway.sms.SMSGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       XMPP                     Description            Handles XMPP instant messaging                    Class            coldfusion.eventgateway.im.XMPPGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       SAMETIME                     Description            Handles Lotus SAMETIME instant messaging                    Class            coldfusion.eventgateway.im.SAMETIMEGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       DirectoryWatcher                     Description            Watches a directory for file changes                    Class            examples.watcher.DirectoryWatcherGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       Socket                     Description            Listens on a socket                    Class            examples.socket.SocketGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       CFML                     Description            Handles asynchronous events through CFCs                    Class            coldfusion.eventgateway.cfml.CfmlGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       JMS                     Description            Handles Java Messaging Service messages                    Class            examples.JMS.JMSGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                       ActiveMQ                     Description            Handles Apache ActiveMQ JMS messages                    Class            examples.ActiveMQ.JMSGateway                    Timeout            30 seconds                   Kill On Timeout            Yes                             Security                         CF Admin Authentication                    Enable authentication for the ColdFusion Administrator            No                    Allow access to ColdFusion Administrator with a Single password            Yes                    Allow concurrent login sessions for Administrator Console            Yes                      RDS Authentication                    Enable authentication for RDS access            Yes                    Allow access through RDS with Single password            Yes                    Secure Profile            No                           Security Sandboxes                         Enable ColdFusion Sandbox Security               No                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "reports/index.cfm"}, {"Category": "Data & Services", "SubCategory": "Data Sources", "Contents": "                                                                                                                                       Data Sources  Add and manage your data source connections and Data Source Names (DSNs). You use a DSN to connect ColdFusion to a variety of data sources.           Add New Data Source                              Data Source Name                                            Driver                                Select a valid driver type:         Apache Derby Client               Apache Derby Embedded               DB2 Universal Database               J2EE Data Source (JNDI)               Microsoft SQL Server               MySQL               MySQL (DataDirect)               Oracle               other               PostgreSQL                                                                        Connected Data Sources                                     Actions                      Data Source Name                      Driver              Status                                                                                                                                                                              cfartgallery                      Apache Derby Embedded                                                                                                                                                                                                cfbookclub                      Apache Derby Embedded                                                                                                                                                                                                cfcodeexplorer                      Apache Derby Embedded                                                                                                                                                                                                cfdocexamples                      Apache Derby Embedded                                                                                                                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "datasources/index.cfm"}, {"Category": "Data & Services", "SubCategory": "NoSQL Data Sources", "Contents": "                                                                                                                                      Add and manage your NoSql data source connections and Data Source Names (DSNs).  You use a DSN to connect ColdFusion to a variety of data sources.          Add New NoSQL Data Source                              Data Source Name                                            Driver                                 Select a valid driver type:                          MongoDB                                                                   Connected NoSQL Data Sources             Action           Data Source Name           Driver                 Status                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "nosql/index.cfm"}, {"Category": "Data & Services", "SubCategory": "ColdFusion Collections", "Contents": "                                                                                                                                         ColdFusion Collections   The Solr indexing engines allows you to easily develop search capabilities for your ColdFusion applications. A Solr collection is a group of information that can be indexed and searched as a set. Use this form to create and manage your Solr collections.                               Unable to retrieve collections from the Search Services.      Ensure that you have installed ColdFusion Search Service and it is running.                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "solr/index.cfm"}, {"Category": "Data & Services", "SubCategory": "Solr Server", "Contents": "                                                                                                                                                                              Solr Server   You can install and configure Solr search service on a host other than the one on which ColdFusion runs. That is the host that Coldfusion will use while performing search operations. Click the Show Advanced Settings to configure these values. You should not need to change the advanced values if you are running with the ColdFusion installed version of Solr.         Configure Solr Server                Solr Host Name                                  Solr Home                                              Advanced Settings                       Solr Admin Port                          Solr Webapp                             Context root of the solr server.                                                                       Solr Buffer Limit                       Size in MB after which, the docs are committed to the Solr Server while indexing. More the buffer limit, better the performance.                                        User name                             Password                                           If basic authentication is enabled on Solr Server, specify the credentials.                                                                             Solr Connection                                                   Use HTTPS connection                                 Solr Admin HTTPS Port                                 Configure Indexing languages                        After adding a stemmer, specify the language and the suffix.                                New language              New language suffix                               Current Languages                                     Language Name                    Language Suffix               Actions                                              Danish                dk                                                                                       Dutch                nl                                                                                       Finnish                fi                                                                                       French                fr                                                                                       German                de                                                                                       Italian                it                                                                                       Norwegian                no                                                                                       Spanish                es                                                                                       Portuguese                pt                                                                                       Russian                ru                                                                                       Swedish                sv                                                                                       Chinese                zh                                                                                       Japanese                ja                                                                                       Korean                ko                                                                                       Czech                cz                                                                                       Greek                el                                                                                       Thai                th                                                                                         Migrate Collections           Migrate old Solr collections.                 Old Solr Home                                                                  -->                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "solr/solrserver.cfm"}, {"Category": "Data & Services", "SubCategory": "Web Services", "Contents": "                                                                                                                                   Web Services       ColdFusion lets you register web services so that you do not have to specify the entire  Web Services Description Language (WSDL) URL when invoking the web service.  ColdFusion automatically registers WSDL URLs the first time they are referenced.         Web Service Version                                      Select web service version                             1             2                                                                     Add / Edit ColdFusion Web Service                             Web Service Name                              WSDL URL                                                                                                           The name assigned to the web service                                                                                                        The absolute URL of the web service's WSDL                                                                                                          Authentication Type                              NONE            BASIC            NTLM                  Timeout                                                                                                  Select authentication type to access the webservice                                                                                                                                    The timeout for the web service request, in seconds                                                                            Username                         Password                                                                                                            The username to use to access the web service.                                                                                                                          The password to use to access the web service.                                                                                      Domain                                           Workstation Name                                                                           The domain for NTLM authentication.                                                                                                                                       The workstation name for NTLM authentication.                                                                                                                                                   Proxy Server                         Proxy Port                                                                                              The proxy server required to access web service URL                                                                                                                             The port to use on the proxy server                                                                                                              Proxy Username                         Proxy Password                                                                                                       The user ID to send to proxy server                                                                                                                 The user's password on the proxy server                                                                                                                                     Active ColdFusion Web Services                              Actions              Web Service Name              WSDL URL                         No Web Services are available.                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/webservices.cfm"}, {"Category": "Data & Services", "SubCategory": "REST Services", "Contents": "                                                                                                                                         REST Services   REST Playground      REST Playground is an interface to register, manage, test, and debug REST services while developing web  services.  To use REST Playground, choose the option   Enable Developer Profile (In Debugging & Logging > Developer Profile)  .  It is recommended to disable REST Playground in Production/Lockdown environments by   disabling the Developer Profile  .                Register your applications and folders. ColdFusion automatically registers CFCs found in the registered folders.         Add/Edit REST Service                                 Root path                                                                        Application path or root folder where CFCs reside                                  Host [:Port]                                                            Host name for the REST service(localhost is default). Example: localhost:8500 (Optional)                                  Service Mapping                                                      Alternate string to be used for application name while calling REST service.  (Optional) Example: http://{Hostname}:{Port}/{REST Path}/{Service Mapping}/{Component REST Path}                               Set as default application                                                                 Set an application as default to exclude the application name in the URL while calling the web service. One default application is allowed for a host.  Example http://{Hostname}:{Port}/{REST Path}/{Component REST Path}                                                                                     Active ColdFusion REST Services                              Actions              Root Path              Service Mapping              Default              Host:Port                         No REST Services are available.                                                        Update REST Path                                                     Change this settings to get customized URL. For example, if you change this setting to 'comservices', URL would look like http://{Hostname}:{Port}/comservices/{ServiceMapping}/{Resource REST Path}                                                                                        © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/restwebservices.cfm"}, {"Category": "Data & Services", "SubCategory": "PDF Service", "Contents": "                                                                                                                                   PDF Service      ColdFusion lets you register multiple PDF Service Managers. These PDF Service Managers will handle PDF conversion requests for CFHtmlToPdf Tag.      Add / Edit PDF Service Manager                               Name                                        Unique name for PDF Service Manager.                    Host Name                          The host name for PDF Service Manager.                     Port                          The port for PDF Service Manager.                              Weight                          The weight for PDF Service Manager.                    Https Enabled                                 If PDF Service Manager is running on https.                            Engine                            1.0          2.0                        The Engine of the Service Manager                                                         PDF Service Managers                                     Actions               Name               Host Name               Port               Weight               Https Enabled               Engine               Connection Status                                                                                                                                                                                                                                                     localhost                      127.0.0.1                     8997                     2                     NO                     2.0                                                                                                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "document/pdfgservice.cfm"}, {"Category": "Data & Services", "SubCategory": "Cloud Credentials", "Contents": "                                                                                                                                     Cloud Credentials   ColdFusion lets you add and manage your Cloud service credentials.     Add / Edit Cloud Credentials                                                 Credential Alias                                                                                                                          Cloud Vendor                                                                 AWS                      AZURE                      GCP                                                                                                AWS Region                                                                                                                          AWS Access Key                                                                                                                          AWS Secret Key                                                                                                                                                                          AWS Session Token                                                                                                                                                 Azure Connection string                                                                                                         Project Id                                                                                                                          Credential JSON Filepath                                                                     Example: EndPoint=sb://(namespace).servicebus.windows.net/;SharedAccessKeyName=(key);SharedAccessKey=(key)                                                          Stored credentials             Action           Cloud Profile Name           Cloud Vendor                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "cloudservices/profiles.cfm"}, {"Category": "Data & Services", "SubCategory": "Cloud Configuration", "Contents": "                                                                                                                                     Cloud Service Configuration    ColdFusion lets you add configurations for your cloud sevices.           Add / Edit Cloud Service Configuration                                                                                                                  Config Alias                                                                                                                                                                                                                           Cloud Vendor                                                                                                 AWS                              AZURE                              GCP                                                                                                                                                                                 Service Name                                                                                                 S3                              SQS                              SNS                              DynamoDB                                                                                BLOB                               Service Bus                                                                                Firestore                              Storage                              Pubsub                                                                                                                                                                                                                                                          Stored Service Configuration             Action           Name           Cloud Service                 Cloud Vendor                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "cloudservices/services.cfm"}, {"Category": "Data & Services", "SubCategory": "GraphQL", "Contents": "                                                                                                                                    GraphQL Config                 GraphQL Service Configuration             Add and manage GraphQL Client configuration                                                                                                                                                                                GraphQL Client Configuration             Add and manage GraphQL Service configuration                                                                                                                   Configuration Alias                                                                                                                                                                                                                           Service Name                                                                                                  Select an option                                                   myservice                                                                                                                                                                                                                                                                                                                                  Defined GraphQL Service Configuration             Action           Service Name                 Service Url / Schema Path                                                                                                                               myservice                                           https://apollo-fullstack-tutorial.herokuapp.com/graphql                               Defined GraphQL Client Configuration             Action           Alias                 Service Name                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "graphql/index.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Debug Output Settings", "Contents": "                                                                                                                                                                     Debug Output Settings             Enable Robust Exception Information           Allow visitors to see the following information in the exceptions page:  Physical path of template  URI of template  Line number and line snippet  SQL statement used (if any)  Data source name (if any)  Java stack trace                 Enable AJAX Debug Log Window          Allows display of the AJAX debug log window when the cfdebug flag is passedin the URL. If you disable this option, the AJAX debug log window does notdisplay, even if the cfdebug flag is specified.               Enable Request Debugging Output          Enables the page-level debugging output on CFML pages.Uncheck this box to override all of the settings below.Debugging information is appended to the end of each request.                        Custom Debugging Output            Select Debugging Output Format      dockable.cfm     classic.cfm     ColdFusion offers several debugging output formats:    classic.cfm  -  The format available in ColdFusion 5 and earlier. It provides a basic view and few browser restrictions.    dockable.cfm  -  A dockable tree-based debugging panel. For details about the panel and browser restrictions, see the online Help.              Report Execution Times     Highlight templates taking longer than the following        (in milliseconds) using the following output mode    summary  tree    Execution times for templates, includes, modules, custom tags, and component method calls. Template execution times over this minimum highlight time appear in red. The default is 250 ms. ColdFusion offers the following template modes:   summary  -  A summary of each page called. Columns include Total Time, Avg Time, Count, and Template. Sorted by highest total time.    tree  -  Hierarchical tree view of individual page executions.  Note: Processing time and output will be longer than summary.                    General Debug Information          Select this option to show general information about this request.General items are ColdFusion Server Version, Template, Time Stamp, User Locale, User Agent, User IP, and Host Name.               Database Activity          Select this option to show the database activity for the SQL Query events and Stored Procedure events in the debugging output.               Exception Information          Select this option to collect all ColdFusion exceptions raised for the request in the debugging output.                 Tracing Information          Select this option to show trace event information in the debugging output.Tracing lets a developer track program flow and efficiency through the use of the CFTRACE tag.               Timer Information          Select this option to show timer event information in the debugging output.Timers let a developer track the execution time of the code between the start and end tags of the CFTIMER tag.               Flash Form Compile Errors and Messages           (Development use only) Select this option to have ColdFusion display ActionScript errors in the browser when compiling Flash forms; this affects the display time of the page.               Variables     Select this option to enable variable reporting. Select the following variables:                Application               Cookie               Server                 CGI               Form               Session                 Client               Request               URL                                  Enable Metrics Logging                    Select this option to enable ColdFusion Metrics Logging.                            Metrics Frequency                       The ColdFusion Metrics will be logged at this frequency.                                  CFSTAT Settings                                           Enable CFSTAT               The cfstat command-line utility provides real-time performance metrics for ColdFusion. Using a socket connection to obtain metric data, cfstat displays the information that ColdFusion writes to System Monitor without actually using the System Monitor application.                                      CFSTAT Host                                                             The IP/host to which the CFSTAT server will bind to. Default value is 127.0.0.1.                                            Connector Port                 The cfstat command-line utility performance metrics for ColdFusion will read performance metrics for this port.                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "debugging/index.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Developer Profile", "Contents": "                                                                                                                               Developer Profile Settings     Enable Developer Profile    Developer profile has to be enabled to use REST Playground ( Tool to test REST services to make REST development faster )  It is recommended to disable developer profile in Production/ Lockdown environments to disable REST Playground.  Following settings are changed when developer profile is enabled:  Trusted Cache is disabled  Robust Exception is enabled  REST Discovery is enabled                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "debugging/devprofile.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Debugging IP Addresses", "Contents": "                                                                                                                               Debugging IP Addresses    Specify the IP addresses that should receive debugging messages, including messages for the AJAX Debug Log window. To include an IP address in the list, enter the address and click Add. To delete an IP address from the list, select the address and click Remove Selected. When no IP addresses are selected, all users receive debugging information.         Select IP Addresses for Debug Output                            IP Address                                                View / Remove Selected IP Addresses for Debug Output                                        127.0.0.1             0:0:0:0:0:0:0:1             ::1                                                                    © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "debugging/iplist.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Debugger Settings", "Contents": "                                                                                                                               Debugger Settings   Enable the Allow Line Debugging option to use the ColdFusion Debugger that runs in Eclipse(ColdFusion Builder). Specify the port and the maximum number of simultaneous debugging sessions.           Line Debugger Settings                                           Allow Line Debugging                              Debugger Port:                                                                 Maximum Simultaneous Debugging Sessions:                                                                                                                  © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "debugging/linedebugger.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Logging Settings", "Contents": "                                                                                                                            Logging Settings     Log directory         Enter the directory where error log files should be written or click Browse Server to select from the directory tree. The drive you specify must have sufficient disk space and security privileges for the ColdFusion system service. You must shut down and restart the ColdFusion Services for this change to take effect.             Maximum file size (in kilobytes)             Enter the maximum file size that ColdFusion should use for log files. When a file reaches this size, it is automatically archived.You must shut down and restart the ColdFusion Services for this change to take effect.              Maximum number of archives             Enter the maximum number of log archives ColdFusion should create. After reaching this limit, files are deleted in order of oldest to newest.               Log slow pages taking longer than    secs         To help diagnose potential problems or bottlenecks in your site, you can have ColdFusion log the names of any pages that take longer than a specified length of time to return. When enabled, any output is written to the server.log file.                    Log all CORBA calls          Logs all CORBA calls to help diagnose configuration issues. When enabled, any output is written to the server.log file.               Enable logging for scheduled tasks          Logs ColdFusion Executive task scheduling.                                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "logging/settings.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Log Files", "Contents": "                                                                                                                                 Log Files    ColdFusion creates several log files that can help you troubleshoot applications and track events.  Use this page to search, view, download, archive and delete log files.           Available Log Files                                         Actions               File Name                 Type                Size                Last Modified                                                                                                                                   application.log                      CFML                    154               29 Jan, 2025 6:57:49 PM                                                                                                                                    audit.log                      CFML                    26482               31 Jan, 2025 2:54:21 PM                                                                                                                        axis2.log                      Other                    1920               31 Jan, 2025 2:48:04 PM                                                                                                                                                    cfpm-audit.log                      CFML                    518550               31 Jan, 2025 2:48:02 PM                                                                                                                        coldfusion-error.log                      Other                    21495               31 Jan, 2025 2:48:04 PM                                                                                                                        coldfusion-out.log                      Other                    66016               31 Jan, 2025 2:49:27 PM                                                                                                                                                    eventgateway.log                      CFML                    595               31 Jan, 2025 2:48:02 PM                                                                                                                        exception.log                      Other                    46829               31 Jan, 2025 12:42:19 PM                                                                                                                                                    http.log                      CFML                    3662               31 Jan, 2025 2:49:27 PM                                                                                                                                                    monitor.log                      CFML                    564               31 Jan, 2025 2:47:06 PM                                                                                                                                    server.log                      CFML                    39701               31 Jan, 2025 2:48:04 PM                                                                                                                                        websocket.log                      Other                    0               29 Jan, 2025 11:21:08 AM                                                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "logging/index.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "System Probes", "Contents": "                                                                                                                                   System Probes    System probes can monitor the health of a web application by checking the contents of a URL at a regular interval. If the contents are not what is expected, probes can send a failure notification email or execute a script.            System Probes                            Actions              Probe Name              Status              Interval              URL                     No probes are defined.                                     Notification email Recipients                                       E-mail                                       Probe.cfm URL                                       Probe.cfm User name                                       Probe.cfm Password                                                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "scheduler/probes.cfm"}, {"Category": "Debugging & Logging", "SubCategory": "Code Analyzer", "Contents": "                                                                                                                            Code Analyzer   The Code Compatibility Analyzer helps migrate your applications to ColdFusion from earlier versions of ColdFusion.The Code Compatibility Analyzer reviews the CFML pages that you specify and informs you of any potential compatibility issues. It detects unsupported and deprecated CFML features, and outlines the required implementation changes that ensure a smooth migration.            Code Compatibility Analyzer             Directory to Analyze                                          Analyze subdirectories        Analyze file types                CFM, CFC      CFM      CFC                                Validate CFML        Version of code to test               CF2023      CF2021      CF2018                                                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "analyzer/index.cfm"}, {"Category": "Performance Monitoring Toolset", "SubCategory": "Monitoring Settings", "Contents": "                                                                                                                                 PMS              ColdFusion Hostname         Enter the IP Address or DNS Name for the ColdFusion instance      Monitoring Shared Secret               Show Secret  -   087b8c16-e016-4652-84dc-fa2c88314724     Secret to configure Performance Monitoring Toolset with ColdFusion       Connected to datastore -        Monitoring Enabled -                                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "pms/index.cfm"}, {"Category": "Extensions", "SubCategory": "CFX Tags", "Contents": "                                                                                                                               CFX Tags   CFX tags are custom tags written against the ColdFusion Application Programming Interface (API) to extend and enhance CFML. You build CFX tags by using C++ as a DLL on Windows platforms and as shared objects (.so/sl) on UNIX platforms. Java CFXs are built by extending the Java Interfaces defined within the cfx.jar file.                                                                                                     Registered CFX Tags               Actions      Tag Name      Type      Description           No CFX tags found.                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/cfx.cfm"}, {"Category": "Extensions", "SubCategory": "Custom Tag Paths", "Contents": "                                                                                                                                   Custom Tag Paths  Custom tags extend the functionality of the ColdFusion Markup Language (CFML).The default custom tag path is under the installation directory. You canspecify other paths to your custom tags here.         Register New Custom Tag Paths              New Path                                             Current Custom Tag Paths             Actions      Path                                                                                                     /Applications/ColdFusion2025/cfusion/CustomTags                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "extensions/customtagpaths.cfm"}, {"Category": "Extensions", "SubCategory": "CORBA Connectors", "Contents": "                                                                                                                               ColdFusion no longer supports corba.                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                          ", "Link": "extensions/corba.cfm"}, {"Category": "Event Gateways", "SubCategory": "Settings", "Contents": "                                                                                                                              Settings                                                      Enable ColdFusion Event Gateway Services         Enables the ColdFusion event gateway services. These services can pass eventsfrom external sources to ColdFusion components that you specify. Changing thissetting starts or stops the services immediately.             Event Gateway Processing Threads           Specifies the maximum number of threads used to execute ColdFusion functionswhen an event arrives. A higher number uses more resources, but increases event throughput. On Standard or Developer Edition, this value can not exceed 1.              Maximum number of events to queue           Specifies the maximum number of events that are allowed in the event queue.If the queue length exceeds this value, gateway events are not added to theprocessing queue. This is a global setting for all event gateways. On Developer Edition, this value can not exceed 10.        SMS Test Server     To assist with the testing of SMS gateway applications, ColdFusion has a built-in SMS test server that works in conjunction with the preconfigured SMS test gateway.  After you  start the SMS test server, you can enable the SMS test gateway and use the SMS test client to test your applications.   The test server is currently stopped.                                             © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "eventgateway/index.cfm"}, {"Category": "Event Gateways", "SubCategory": "Gateway Types", "Contents": "                                                                                                                                        Gateway Types   Configure the types of gateways available on your system.  After you configure a type, you can create any number of gateway instances of that type.          Add / Edit ColdFusion Event Gateway Types                               Type Name                                         Description                                           Java Class                                    Startup Timeout(in seconds)                                                           Stop on Startup Timeout                                                           Configured ColdFusion Gateway Types                             Actions              Name              Description              Java Class              Timeout              Kill on Timeout?                                                                                                                                                                    ActiveMQ                               Handles Apache ActiveMQ JMS messages                   examples.ActiveMQ.JMSGateway                  30                  YES                                                                                                                                                                        CFML                               Handles asynchronous events through CFCs                   coldfusion.eventgateway.cfml.CfmlGateway                  30                  YES                                                                                                                                                                        DirectoryWatcher                               Watches a directory for file changes                   examples.watcher.DirectoryWatcherGateway                  30                  YES                                                                                                                                                                        JMS                               Handles Java Messaging Service messages                   examples.JMS.JMSGateway                  30                  YES                                                                                                                                                                        SAMETIME                               Handles Lotus SAMETIME instant messaging                   coldfusion.eventgateway.im.SAMETIMEGateway                  30                  YES                                                                                                                                                                        SMS                               Handles SMS text messaging                   coldfusion.eventgateway.sms.SMSGateway                  30                  YES                                                                                                                                                                        Socket                               Listens on a socket                   examples.socket.SocketGateway                  30                  YES                                                                                                                                                                        XMPP                               Handles XMPP instant messaging                   coldfusion.eventgateway.im.XMPPGateway                  30                  YES                                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "eventgateway/gatewaytypes.cfm"}, {"Category": "Event Gateways", "SubCategory": "Gateway Instances", "Contents": "                                                                                                                                        Gateway Instances   You can configure ColdFusion event gateway instances to direct events from various sources to ColdFusion components that you have written.         Add / Edit ColdFusion Event Gateway Instances                            Gateway ID                                            Gateway Type                    Please select a type              ActiveMQ - Handles Apache ActiveMQ JMS messages               CFML - Handles asynchronous events through CFCs               DirectoryWatcher - Watches a directory for file changes               J<PERSON> - <PERSON>les Java Messaging Service messages               SAMETIME - <PERSON>les Lotus SAMETIME instant messaging               SMS - Handles SMS text messaging               Socket - Listens on a socket               XMPP - Handles XMPP instant messaging                                               CFC Path                                                     Configuration File                                                     Startup Mode                     Automatic        Manual        Disabled                                                                    Configured ColdFusion Event Gateway Instances                            Actions              Status              Gateway ID              Type              Startup              In              Out              CFC Path              Gateway Config                                                                                                                                                                                                                                                  Stopped                       SMS Menu App - 5551212                 SMS                manual                0               0               {cf.rootdir}/gateway/cfc/examples/menu/main.cfc                {cf.rootdir}/gateway/config/sms-test.cfg                                                                     © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "eventgateway/gateways.cfm"}, {"Category": "Security", "SubCategory": "Administrator", "Contents": "                                                                                                                                          Administrator    ColdFusion Administration Authentication            You should restrict access to the ColdFusion Administrator to trusted  users. By default the ColdFusion Administrator requires authentication to access  these pages. However, if you configure your web server to restrict access to  these pages, you can disable this authentication and  rely on your web server's security instead. (Consult your web server  documentation for details on securing pages.)           Select the type of Administrator authentication:           Use a single password only (default)           Separate user name and password authentication (allows multiple users)         No authentication needed (not recommended)              Root Administrator Password         To change the ColdFusion Administrator password for the root administrative user,enter old password, new password and then confirm        Old Password          New Password          Confirm Password                 Password Seed         To specify a new seed value to encrypt data source passwords    New Seed    (8-character minimum and 500-character limit.)         Concurrent Login Session             Allow concurrent login sessions for Administrator Console        External Authentication               NONE                              SAML             Identity Provider:            --Select an option--               Service Provider:            --Select an option--               SAML groupName alias:   i Allows users of added group to login with it's permissions.                       LDAP          Add LDAP Configuration    Edit LDAP Configuration    Verify Connection    Connection verified      Connection not verified                                                                        © 1995 - 2025 Adobe. All Rights Reserved.                                                                                                                  LDAP DETAILS                        Connection                                        Host*  i Hostname or IP Address of ldap server                                                      TCP Port*  i Port number for communication to ldap server                                                                                 User Bind DN  i Connect to server using supplied Bind DN. ex: ou=admin,ou=adobe,c=com.Leave blank to connect anonymously.                                                      User Bind Password  i Connect to directory using supplied password                                                                                              User BaseContext*  i Start point in Ldap tree for searching users. Ex: ou=users, dc=adobe, dc=com                                                                                         Group BaseContext*  i Start point in Ldap tree for searching groups. Ex: ou=groups, dc=adobe, dc=com                                                                           Server Timeout*  i Time Limit to search ldap server in milliseconds                                                                     SSL/TLS  i Secures Connection. Server should use specified port for ssl connection. Truststore used by JVM should have necessary certificates installed.                                     Start TLS  i Sends secure and plain requests against Ldap server on single connection. Server should use specified port for clear-text ldap connection                                                        Configuration                            User Configuration  i Object Class of users                             Add                                                          Username Attribute*  i Attribute which holds account's username                                                                                 Group Configuration  i Object Class of groups                                Add                                                           Groupname Attribute*  i Attribute which holds groupname                                                          Save       Close                ", "Link": "security/cfadminpassword.cfm"}, {"Category": "Security", "SubCategory": "RDS", "Contents": "                                                                                                                                   RDS                Enable RDS Service                    The ColdFusion RDS service allows you to connect to this server using the RDS password you define below.   This is intended for development use only. If this is a production machine, leave this option unchecked.               RDS authentication      You should restrict access to RDS to trusted users. By default RDS requires authentication. However, you may disable this authentication and rely on your web server's security instead.       Select the type of RDS authentication:           Use a single password only (default)            Separate user name and password authentication (allows multiple users)          No authentication needed (not recommended)                   RDS Single Password        To change the single RDS password, enter old password, new password and then confirm          Old Password             New Password             Confirm Password            (50-character limit.)                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/cfrdspassword.cfm"}, {"Category": "Security", "SubCategory": "Sandbox Security", "Contents": "                                                                                                                                       Sandbox Security                                                     Enable ColdFusion Sandbox Security          ColdFusion's sandbox security uses the location of your ColdFusion pages to control access to ColdFusion resources. A sandbox is a designated area (files or directories) of your site to which you apply security restrictions. By default, a subdirectory (or child directory) inherits the sandbox settings of the directory one level above it (the parent directory). If you define sandbox settings for a subdirectory, you override the sandbox settings inherited from the parent directory.       Note:   You can configure these settings prior to enabling them on the server. Also, you must restart the ColdFusion application server to enable this setting.                      Add Security Sandbox                                                                             New sandbox, or pick one to copy from                                                                                       Defined Directory Permissions                                         Actions                 Directory                                                            © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/index.cfm"}, {"Category": "Security", "SubCategory": "User Manager", "Contents": "                                                                                                                                   User Manager    Add and manage users.  Users can be granted access to roles and sandboxes.                                                     Defined Users                Action           User           User Type           Description           RDS           Administrative Access                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/usermanager.cfm"}, {"Category": "Security", "SubCategory": "Allowed IP Addresses", "Contents": "                                                                                                                              Allowed IP Addresses     Specify the client IP addresses that can access ColdFusion Administrator and ColdFusion Internal Directories. This can be individual IP addresses, IP address ranges of theform 10-30, or * wild cards. Both IPv4 and IPv6 addresses are supported. To include an IP address in the list, enter theaddress and click Add. To delete an IP address from the list, select the address and click Remove Selected. When no IPaddresses are selected, all users are allowed access.         Allowed IP Addresses for accessing ColdFusion Administrator and ColdFusion Internal Directories                                            IP Address                                                                            127.0.0.1             ::1                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "security/allowedipaddress.cfm"}, {"Category": "Security", "SubCategory": "Secure Profile", "Contents": "                                                                                                                              Secure Profile                                             Enable Secure Profile    Secure Profile settings are only a recommendation. You should further configure the server as per your requirements. The settings affected by this are listed in table below.     Secure Profile Settings Summary        Setting Name   Current Value   Secure Default Value        Enable Robust Exception Information             false           false            RDS separate UserID Disabled             true           false            Start Flash Policy Server             true           false            Missing Template Error Handler                         /CFIDE/administrator/templates/missing_template_error.cfm            Admin separate UserID Required             false           true            RDS Authentication Enabled             true           true            Disable access to internal ColdFusion Java components             false           true            Enable RDS             true           true            Maxium size of post data (MB)             20.0           20.0            Request Queue Timeout Page                         /CFIDE/administrator/templates/request_timeout_error.htm            Session Cookie Timeout (Minutes)             15768000           1440            Admin Authentication Enabled             false           true            Use UUID for cftoken             true           true            Allow concurrent login sessions for Administrator Console             true           false            Disable updating of ColdFusion internal cookies             false           true            Enable WebSocket Server             true           false            Allowed file extensions for CFInclude tag             *           CFM,CFML            Enable CFSTAT             true           false            Enable Global Script Protection             true           true                Allowed IP Addresses             127.0.0.1,::1           127.0.0.1,::1            Site Error Handler                         /CFIDE/administrator/templates/secure_profile_error.cfm            Allow create, drop, alter, grant, revoke, stored procedures for DSNs           drop,storedproc,revoke,create,alter,grant : true,true,true,true,true,true     drop,storedproc,revoke,create,alter,grant : false,false,false,false,false,false                              © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "security/secureprofile.cfm"}, {"Category": "Security", "SubCategory": "IDP Configuration", "Contents": "                                                                                                                                    IDP Config      Add and manage SAML Identity Providers.                                                    Defined Identity Providers                Action           Name           Description                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/idpconfig.cfm"}, {"Category": "Security", "SubCategory": "SP Configuration", "Contents": "                                                                                                                                    SP Config         Add and manage SAML Service Providers.                                                                                                                                                                                                                                                                                                                                     Defined Service Providers             Action           Name           Description                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "security/spconfig.cfm"}, {"Category": "Packaging & Deployment", "SubCategory": "ColdFusion Archives", "Contents": "                                                                                                                                          ColdFusion Archives     Enter the path or browse to the appropriate CAR file to deploy the files to this server and update the relevant server settings.                  Deploy an Existing Archive                                                                                                               ColdFusion lets you define applications for organizing work,     archiving files, migrating and deploying sites. You can create and     store ColdFusion Archive definitions to archive, migrate, or redeploy     applications at a later date.                                                Create an archive                                                  Archive Name                                                                            Current Archive Definition List                                         Actions                 Archive Name                              No Archives have been defined                                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "archives/index.cfm"}, {"Category": "Packaging & Deployment", "SubCategory": "JEE Archives", "Contents": "                                                                                                                             JEE Archives        Create a JEE archive (EAR or WAR) file.          Add New Archive                             Archive Name                                                           Configured Archives                             Actions              Archive Name              Type              Last Built                        No Archives have been defined                                               © 1995 - 2025 Adobe. All Rights Reserved.                                                                                      ", "Link": "j2eepackaging/index.cfm"}, {"Category": "Enterprise Manager", "SubCategory": "Instance Manager", "Contents": "                                                                                                                                 Instance Manager            ColdFusion Enterprise lets you create and manage new server instances. These can be created and run locally (on this machine)        or they can be remote servers that you can register with clusters.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Available Servers                                                       Filter by Cluster                                                                                  >                                                                                                                                      Actions            Name            Server Directory            HTTP Port            Remote Port            Host            Cluster                                                                                                                                                                                                                                                             cfusion              /Applications/ColdFusion2025/cfusion               8500             8024             localhost                      none                                                             © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "entman/index.cfm"}, {"Category": "Enterprise Manager", "SubCategory": "Cluster Manager", "Contents": "                                                                                                                                Cluster Manager                    Add New Cluster                             Cluster Name                                                                       Configured Clusters                           Actions            Cluster Name            Servers In Cluster                  No Clusters are defined.                                           © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "entman/cluster.cfm"}, {"Category": "Package Manager", "SubCategory": "Packages", "Contents": "                                                                                                                                              Updates                       When clicked, exports the list of all the installed packages that can be applied to other ColdFusion instance(s) using import command.                                                            Core Server                                                                                                                                                     There are no updates to Core package.                                                                                                                                                  Name                                                                              Update Level:                                                                              Update Type:                                                                              Build Number:                                                                              Update Description:                                                                              Technote Link                                                                               Install Date:                                                                              Backup Directory:                                                                              Uninstaller Location:                                                                              Install Log Directory:                                                                              Available Versions                                                                                                                        There were errors in the previous install of this update.Please refer to the logs in the folder    and fix the root cause before re-applying the hotfix again.                                                                          Starting Download...                                                                                                                                                                                                                                                                                                     Installed Packages                           These packages are already installed on the system.                                                Uninstall All                                                                                                                                                                                                                                                                                                                                                           adminapi                                                                                                                                                                                                                                                            administrator                                                                                                                                                                                                                                                            ajax                                                                                                                                                                                                                                                            awsdynamodb                                                                                                                                                                                                                                                            awslambda                                                                                                                                                                                                                                                            awss3                                                                                                                                                                                                                                                            awss3legacy                                                                                                                                                                                                                                                            awssns                                                                                                                                                                                                                                                            awssqs                                                                                                                                                                                                                                                            axis                                                                                                                                                                                                                                                            azureblob                                                                                                                                                                                                                                                            azureservicebus                                                                                                                                                                                                                                                            caching                                                                                                                                                                                                                                                            ccs                                                                                                                                                                                                                                                            cfmongodb                                                                                                                                                                                                                                                            chart                                                                                                                                                                                                                                                            db2                                                                                                                                                                                                                                                            debugger                                                                                                                                                                                                                                                            derby                                                                                                                                                                                                                                                            document                                                                                                                                                                                                                                                            dotnet                                                                                                                                                                                                                                                            eventgateways                                                                                                                                                                                                                                                            exchange                                                                                                                                                                                                                                                            feed                                                                                                                                                                                                                                                            ftp                                                                                                                                                                                                                                                            gcpfirestore                                                                                                                                                                                                                                                            gcppubsub                                                                                                                                                                                                                                                            gcpstorage                                                                                                                                                                                                                                                            graphqlclient                                                                                                                                                                                                                                                            htmltopdf                                                                                                                                                                                                                                                            image                                                                                                                                                                                                                                                            mail                                                                                                                                                                                                                                                            msgraph                                                                                                                                                                                                                                                            mysql                                                                                                                                                                                                                                                            oracle                                                                                                                                                                                                                                                            orm                                                                                                                                                                                                                                                            ormsearch                                                                                                                                                                                                                                                            pdf                                                                                                                                                                                                                                                            pmtagent                                                                                                                                                                                                                                                            postgresql                                                                                                                                                                                                                                                            presentation                                                                                                                                                                                                                                                            print                                                                                                                                                                                                                                                            redissessionstorage                                                                                                                                                                                                                                                            report                                                                                                                                                                                                                                                            saml                                                                                                                                                                                                                                                            scheduler                                                                                                                                                                                                                                                            search                                                                                                                                                                                                                                                            sharepoint                                                                                                                                                                                                                                                            spreadsheet                                                                                                                                                                                                                                                            sqlserver                                                                                                                                                                                                                                                            websocket                                                                                                                                                                                                                                                            zip                                                                                                                                                        Name                                                                              Installed Version                                                                              Category                                                                              Description                                                                              Required ColdFusion Packages                                                                              Required Jars                                                                                                  Available Versions                                                                                                  Install Status                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Confirmation                  Packages Administrator and <PERSON><PERSON><PERSON> will not be uninstalled.\">                    Are you sure you want to uninstall all packages? Packages Administrator and <PERSON><PERSON><PERSON> will not be uninstalled.                                                                                  OK                          Cancel                                                                                                                  Confirmation                                       Are you sure you want to uninstall package   {}   ?                                                                                                         OK                          Cancel                                                                                                                  Confirmation                                       Are you sure you want to update all packages?                                                                                                         OK                          Cancel                                                                                Available Packages                   No more packages available to install.                                                                                                            Name                                                                              Category                                                                              Description                                                                              Required ColdFusion Packages                                                                              Required Jars                                                                                                  Available Versions                                                                                                                      Install Status                                                                                                                                                                                                                                                                                                                                                                                                                         Confirmation                                       Are you sure you want to install all packages?                                                                                                        OK                          Cancel                                                                                                                   Confirmation                                                                                                                                                                    OK                          Cancel                                                                                                                          Uninstall Confirmation             Do you want to uninstall update?     ColdFusion server will be stopped and restarted during uninstall process. Uninstaller will remove all the files from the ColdFusion instances to which you applied this hotfix.                                                                                                Progress Information                 ColdFusion server will be stopped and restarted during uninstall process. Uninstaller will remove all the files from the ColdFusion instances to which you applied this hotfix.     Starting uninstallation...this might take a few minutes           Uninstall Status: Checking...                                                                                                          Progress Information                                                                                                                      Progress Information                                                                                                            Progress Information                                                                                                                                                                                                                                                              Confirm overwrite                       Update file already exists.       Do you want to overwrite existing file?                                                                   © 1995 - 2025 Adobe. All Rights Reserved.                                                                                          ", "Link": "updates/index.cfm"}, {"Category": "Package Manager", "SubCategory": "Settings", "Contents": "                                                                                                                                                         Auto-Check                           Automatically Check for Updates                 Select to automatically check for updates at every login.                          Notification                               Check for updates every       days                         If updates are available, send email notification to           (ex. <EMAIL>,<EMAIL>)                     If updates are available, send email notification from               (ex. <EMAIL>)                          Update Site                     Site URL                          If you have set up a local update site, specify URL of that site to get updates.                       Packages Site                     Site URL                          If you have set up a local packages site, specify URL of that site to get updates.                   Proxy Settings                             Proxy Host                                                Proxy Port                                                Proxy Username                                                Proxy Password                                                                       © 1995 - 2025 Adobe. All Rights Reserved.                                                                                       ", "Link": "updates/_settings.cfm"}, {"Category": "Licensing and Activation", "SubCategory": "Activation", "Contents": "                                                                                                                                                                      The Licensing and Activation page allows you to manage your ColdFusion licenses and track usage of the instances.               Product Information                                                                              Server Edition    Developer                                                                            Deployment Type    Development                                                                                  Hostname    Parvathys-MacBook-Pro.local                                                        Device ID    29c8c3c0d5146c294ef6b2b9615fa0f0e5f419a3a8142b71d361215b5ae9e738                                                                                        License Information                                                                                                     Activation Mode     Named User License (NUL)      NUL                                                                                             Activation Status   Not active                                                                                                                                                                                             Activate License                                                                              License Mode                                                                                                                    Named User License (NUL)                          Feature Restricted Licensing (Online/Offline)                          Feature Restricted Licensing (Isolated)       FRL                                                                                                                  Deployment Type                                                                                                                Staging                                                      Testing                                                      Development                                                      Disaster Recovery                                                      Production                                                                                                                                 Activation Code                                                           BBBU7Y-KJWSS2-FSDDSQ                                                                                                                                                      Copy and use this activation code in admin console to generate the license file                                                                                                License Path                                                                                                                                                                                                                                                            Activate                              Activate                                                                                                                                   Activate Offline               If your computer is permanently offline(i.e. secure environments like Government, banking etc), you can use the offline mode of activation.              Click the \"Generate Activation Request\" button to generate an activation request file and upload the file on    https://www.adobe.com/go/coldfusion-activate                Please enter the serial number above for offline activation.               Generate Activation Request                                                  Use the activation response file from the above step and click \"Submit\" to activate ColdFusion.                                           Activation Response :                                   Upload                                        Submit                                          Note - If the activation process cannot be completed within   72 hours , you will need to generate new activation request.                                                               Confirmation                                       You are about to deactivate ColdFusion. After deactivation, ColdFusion will revert to the Developer or Trial edition.                      Do you want to proceed?                                                                                   Yes                          No                                                                                                 © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "activation/activation.cfm"}, {"Category": "Licensing and Activation", "SubCategory": "Usage", "Contents": "                                                                                                                                                                           The Usage page gives an analytical view of your license’s usage in terms of number of instances used, units used, usage graph, and much more.          The Usage data does not apply to this edition of ColdFusion.                                          © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "activation/usage.cfm"}, {"Category": "Licensing and Activation", "SubCategory": "Settings", "Contents": "                                                                                                                                                       Notification                         You can choose to receive email notifications when your subscription is about to expire. The first notification will be sent 90 days before the subscription expires,and subsequent notifications will be sent based on the frequency you select below.Please note,you will be able to send and receive notifications only if you check the option below and have configured mailserver.                                                      Notify every          day(s).                         Send email notification to          (ex. <EMAIL>,<EMAIL>)                    Send email notification from              (ex. <EMAIL>)                                 Auto Restart Instances                                         Enable if you want the instances to be automatically restarted after activation, deactivation, or changing usage data settings.                                       Proxy Server Settings                                           To use the proxy server, provide the settings in the  Package Manager > Settings  page.                                                      © 1995 - 2025 Adobe. All Rights Reserved.                                                                                        ", "Link": "activation/settings.cfm"}]
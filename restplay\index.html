   <!doctype html>
   <html>
    <head>
    <meta charset="utf-8">
    <title>ColdFusion REST Playground</title>
    <link rel="stylesheet" href="css/bootstrap.css"/>
    <link rel="stylesheet" href="css/ngToast.min.css"/>
    <link rel="stylesheet" href="css/select.css"/>
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    <link rel="stylesheet" type="text/css" href="fonts/opensans.css"/>
    <script type="text/javascript" src="js/require.js"></script>
    <script type="text/javascript" src="js/ace.js"></script>
    <script type="text/javascript">
        requirejs.config({
            //By default load any module IDs from js/lib
            baseUrl: 'js/',
            //except, if the module ID starts with "app",
            //load it from the js/app directory. paths
            //config is relative to the baseUrl, and
            //never includes a ".js" extension since
            //the paths config could be for a directory.
            
        });

        require([
            'jquery',
            'angular'
            
        ], function () {
            require([ 'js/app.js'], function () {
                 angular.bootstrap(document, ['cfrest']);
            })
        });
    </script>       
</head>
    

    <body>
     <toast></toast>
    <div class="cfrest" ng-view>
    
      <!-- Main View -->    
    
    
    </div>
     <rest-spinner></rest-spinner>

    </body>
    </html>
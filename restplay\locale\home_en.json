{"PageTitle": "REST Playground", "PageTitleBar": "ColdFusion REST Playground", "Applications": "Applications", "AddApp": "Add Application", "Search": "Search", "ShowError": "Click to see error", "RefreshApp": "Refresh App", "NoAppAvailable": "No Applications Available", "Request": "Request", "URLCopied": "URL Copied to Clipboard", "PressCopy": "Press Ctrl+c to Copy", "Headers": "Headers", "Parameters": "Parameters", "Body": "Body", "Name": "Name", "Value": "Value", "AddHeader": "<PERSON>d <PERSON>", "AddParameter": "Add Parameter", "AddValue": "Add Value", "Send": "Send", "Response": "Response", "Status": "Status", "Documentation": "Documentation", "Description": "Description", "NoDesc": "No Description Available", "NoParamsAvailable": "No Parameters Available", "ParamName": "Name", "ParamType": "Type", "ParamRealType": "Parameter Type", "AppPath": "Application Path", "AppHost": "Host", "ServiceMapping": "Service Mapping", "SetDefaultApp": "Set as default application", "Register": "Register", "Cancel": "Cancel", "EnableDevProfile": "Enable Developer Profile in ColdFusion Administrator(Under Debugging & Logging).This updates all the dependant settings(REST Discovery/Trusted Cache/Robust Exception).Enabling Developer Profile is not recommended  in production/ lock-down environments", "ErrorFetchApplications": "Error Fetching Applications", "DeleteApp": "Delete Application", "DeleteAppName": "Are you sure you want to delete the application {} ?", "CouldNotDelete": "Applciation could not be deleted", "ErrorUpdateApps": "Error Updating the Applications", "ErrorFetchConf": "Error fetching the configuration.", "EnterValidApp": "Please enter valid application path.", "ServiceNotRegistered": "Service could not be registered", "ServiceRegistered": "Service Registered successfully", "RegisterApp": "Register Application", "NoAppMsg": "Register ColdFusion applications to start using REST APIs.<br/> No applications are registered.", "RequestSummary": "Request Summary", "URL": "Request URL", "Method": "Request Method", "TipAppPath": "Application path or root folder where CFCs reside", "TipAppHost": "Host name for the REST service. Example: localhost:8500 (Optional)", "TipServiceMapping": "Use an alternate string for the application name while calling a REST service. Example: http://localhost/rest/{service mapping}/test (Optional)", "TipDefaultApp": "Set an application as default to exclude the application name in the URL while calling the web service. One default application is allowed for a host. Example http://localhost/rest/path", "Error": "Error"}
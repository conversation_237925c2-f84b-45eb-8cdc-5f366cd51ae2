<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE web-app PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN" "http://java.sun.com/dtd/web-app_2_3.dtd">
<web-app id="adobe_coldfusion">
    <display-name>Adobe ColdFusion Splendor</display-name>
    <description>Adobe ColdFusion Splendor</description>
    <context-param id="coldfusion_context_1">
        <param-name>cftags</param-name>
        <param-value>/WEB-INF/cftags</param-value>
        <description>Path to search for built-in tags. Relative to application root.
            This parameter can only be one path element.</description>
    </context-param>
    <context-param id="coldfusion_context_2">
        <param-name>coldfusion.compiler.outputDir</param-name>
        <param-value>/WEB-INF/cfclasses</param-value>
        <description>This is the directory where we will place compiled
            pages. It must be relative to the webapp root.</description>
    </context-param>
	<!-- begin CF Reporting -->
    <context-param id="coldfusion_context_4">
        <param-name>cfx.registry.nativelibrary</param-name>
        <param-value>cfregistry</param-value>
        <description>Native library that implements CFX_REGISTRY.
            Used on Windows only.</description>
    </context-param>
	<!-- end CF Reporting -->
    <context-param id="coldfusion_context_5">
        <param-name>cfx.report.nativelibrary</param-name>
        <param-value>cfreport</param-value>
        <description>Native library that implements CFX_REPORT.
            Used on Windows only.</description>
    </context-param>
    <context-param id="coldfusion_context_88">
        <param-name>cf.class.path</param-name>
        <param-value>
            ./WEB-INF/cfusion/lib/updates,./WEB-INF/cfusion/lib,./WEB-INF/cfusion/gateway/lib,./WEB-INF/cfform/jars,./WEB-INF/cfusion/lib/axis2</param-value>
    </context-param>
    <context-param id="coldfusion_context_89">
        <param-name>cf.lib.path</param-name>
        <param-value>./WEB-INF/cfusion/lib</param-value>
    </context-param>

    <!-- CF Monitoring Filter  -->
    <filter>
        <filter-name>CFMonitoringFilter</filter-name>
        <filter-class>coldfusion.bootstrap.BootstrapFilter</filter-class>
        <init-param>
            <param-name>filter.class</param-name>
            <param-value>coldfusion.monitor.event.MonitoringServletFilter</param-value>
        </init-param>
    </filter>

    <!-- Cache Filter for cfform swfs -->
	<!-- begin CFSwf -->
    <filter>
        <filter-name>CFCacheFilter</filter-name>
        <filter-class>coldfusion.bootstrap.BootstrapFilter</filter-class>
        <init-param>
            <param-name>filter.class</param-name>
            <param-value>flex.server.j2ee.cache.CacheFilter</param-value>
        </init-param>
    </filter>
    <!-- end CFSwf -->
     
	<!-- CF ClickJacking deny protection Filter  -->
    <filter>
        <filter-name>CFClickJackFilterDeny</filter-name>
        <filter-class>coldfusion.bootstrap.BootstrapFilter</filter-class>
        <init-param>
            <param-name>filter.class</param-name>
            <param-value>coldfusion.filter.ClickjackingProtectionFilter</param-value>
		</init-param>
		<init-param>
			<param-name>mode</param-name>
            <param-value>DENY</param-value>
        </init-param>
    </filter>
	
	<!-- CF ClickJacking same origiin protection Filter  -->
    <filter>
        <filter-name>CFClickJackFilterSameOrigin</filter-name>
        <filter-class>coldfusion.bootstrap.BootstrapFilter</filter-class>
        <init-param>
            <param-name>filter.class</param-name>
            <param-value>coldfusion.filter.ClickjackingProtectionFilter</param-value>
		</init-param>
		<init-param>
			<param-name>mode</param-name>
            <param-value>SAMEORIGIN</param-value>
        </init-param>
    </filter>
	
	<!-- CF ClickJacking Filter mapppings starts. For ColdFusion Administrator we are allowing sameorigiin frames. Use Deny or some other mode of this filter as appropriate for the application and add required url pattern
	-->
	<filter-mapping>
        <filter-name>CFClickJackFilterSameOrigin</filter-name>
        <url-pattern>/CFIDE/administrator/*</url-pattern>
    </filter-mapping>
	<!-- End CF ClickJacking Filter mappings -->

    <!-- CF Monitoring Filter mappings
     When new servlets registered in web.xml, it must be ensured that
     filter mappings are added for this filter as required.
     Note that the MessageBrokerServlet for the Flex 2 Gateway is not
     included here. This is because Flex appears to batch invocations
     for methods invoked close to one another, resulting in the filter
     being missed for all batched invocations other than the first.
    -->
    <filter-mapping>
        <filter-name>CFMonitoringFilter</filter-name>
        <servlet-name>CfmServlet</servlet-name>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CFMonitoringFilter</filter-name>
        <servlet-name>CFCServlet</servlet-name>
    </filter-mapping>
    <!-- End CF Monitoring Filter mappings -->

    <!-- end request control filters -->

    <!-- Cache Filter for cfform swfs -->
	<!-- begin CFSwf -->
    <filter-mapping>
        <filter-name>CFCacheFilter</filter-name>
        <servlet-name>CFSwfServlet</servlet-name>
    </filter-mapping>
    <!-- end CFSwf -->

    <!--Commenting HttpFlexSessionBootstrap listener since flex is not supported anymore. -->
    <!--
    <listener>
        <listener-class>coldfusion.bootstrap.HttpFlexSessionBootstrap</listener-class>
    </listener>
    -->

    <!-- start flex2
    <servlet>
        <servlet-name>FlexMxmlServlet</servlet-name>
        <display-name>MXML Processor</display-name>
        <description>Servlet wrapper for the Mxml Compiler</description>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param>
            <param-name>servlet.class</param-name>
            <param-value>flex.webtier.server.j2ee.MxmlServlet</param-value>
        </init-param>
        <init-param>
            <param-name>webtier.configuration.file</param-name>
            <param-value>/WEB-INF/flex/flex-webtier-config.xml</param-value>
        </init-param>
        <init-param>
            <param-name>mxml.swf.extension</param-name>
            <param-value>false</param-value>
        </init-param>
       <load-on-startup>12</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>FlexInternalServlet</servlet-name>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param>
            <param-name>servlet.class</param-name>
            <param-value>flex.webtier.server.j2ee.filemanager.FileManagerServlet</param-value>
        </init-param>
        <load-on-startup>13</load-on-startup>
    </servlet>
    end flex2 -->

    <servlet id="coldfusion_servlet_1">
        <servlet-name>ColdFusionStartUpServlet</servlet-name>
        <display-name>Coldfusion MX Startup Servlet</display-name>
        <description>Initializes ColdFusion</description>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param id="InitParam_1034013110641">
            <param-name>servlet.class</param-name>
            <param-value>coldfusion.server.j2ee.CFStartUpServlet</param-value>
        </init-param>
        <init-param id="InitParam_1034013110642">
            <param-name>cfRootDir</param-name>
            <param-value>./WEB-INF/cfusion</param-value>
        </init-param>
        <init-param id="InitParam_1034013110643">
            <param-name>appServer</param-name>
            <param-value>J2EE</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>


    <servlet id="coldfusion_servlet_3">
        <servlet-name>CfmServlet</servlet-name>
        <display-name>CFML Template Processor</display-name>
        <description>Compiles and executes CFML pages and tags</description>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param id="InitParam_1034013110656ert">
            <param-name>servlet.class</param-name>
            <param-value>coldfusion.CfmServlet</param-value>
        </init-param>
        <load-on-startup>4</load-on-startup>
    </servlet>
    <servlet id="coldfusion_servlet_5">
        <servlet-name>CFCServlet</servlet-name>
        <display-name>CFC Processor</display-name>
        <description>Compiles and executes CF web components</description>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param id="InitParam_1034013110657ax">
            <param-name>servlet.class</param-name>
            <param-value>coldfusion.xml.rpc.CFCServlet</param-value>
	   </init-param>
        <load-on-startup>10</load-on-startup>
    </servlet>

        <!-- Used in calling OnServerStart, so this should be the last one to get initialized -->
        <servlet id="coldfusion_servlet_1001">
            <servlet-name>ServerCFCServlet</servlet-name>
            <display-name>OnServerStart Servlet</display-name>
            <description>Invokes OnServerStart</description>
            <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
            <init-param id="InitParam_1034013110613">
                <param-name>servlet.class</param-name>
                <param-value>coldfusion.cfc.ServerCFCServlet</param-value>
            </init-param>
            <init-param id="InitParam_1034013110614">
                <param-name>cfRootDir</param-name>
                <param-value>./WEB-INF/cfusion</param-value>
            </init-param>
            <load-on-startup>1001</load-on-startup>
        </servlet>
		
		<!-- begin REST -->
		<servlet id="coldfusion_servlet_6">
			<servlet-name>CFRestServlet</servlet-name>
			<display-name>Rest Processor</display-name>
			<description>Starts and configures rest web components</description>
			<servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
			<init-param id="InitParam_1034013110657rs">
				<param-name>servlet.class</param-name>
				<param-value>coldfusion.rest.servlet.CFRestServlet</param-value>
		   </init-param>
		    <init-param>
                <param-name>com.sun.jersey.spi.container.ContainerRequestFilters</param-name>
                <param-value>coldfusion.rest.servlet.CFUriConnegFilter;com.sun.jersey.api.container.filter.GZIPContentEncodingFilter;coldfusion.rest.servlet.CFRequestFilter</param-value>
            </init-param>
            <init-param>
                <param-name>com.sun.jersey.spi.container.ContainerResponseFilters</param-name>
                <param-value>coldfusion.rest.servlet.CFResponseFilter;com.sun.jersey.api.container.filter.GZIPContentEncodingFilter</param-value>
            </init-param>
			<load-on-startup>11</load-on-startup>
		</servlet>
		<!-- end REST -->

    <!-- begin RDS -->
    <servlet id="coldfusion_servlet_8789">
        <servlet-name>RDSServlet</servlet-name>
        <display-name>RDS Servlet</display-name>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param id="InitParam_103401311065856789">
            <param-name>servlet.class</param-name>
            <param-value>coldfusion.rds.RdsFrontEndServlet</param-value>
        </init-param>
    </servlet>
    <!-- end RDS -->
	 <!-- begin JS Debug -->
    <servlet id="coldfusion_servlet_9000">
        <servlet-name>JSDebugServlet</servlet-name>
        <display-name>JSDebugServlet</display-name>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param id="InitParam_103401311065856790">
            <param-name>servlet.class</param-name>
            <param-value>coldfusion.debugger.js.JSDebuggingServlet</param-value>
        </init-param>
    </servlet>
    <!-- end JS Debug -->

    <servlet>
        <servlet-name>CFFileServlet</servlet-name>
        <display-name>Serves files for cfpresentation,cfreport,captcha etc</display-name>
        <servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
        <init-param>
            <param-name>servlet.class</param-name>
            <param-value>coldfusion.util.CFFileServlet</param-value>
        </init-param>
    </servlet>


    <!-- WSRP producer servlet.. -->
	<!-- begin WSRP -->
    <servlet>
      	<servlet-name>WSRPProducer</servlet-name>
      	<servlet-class>coldfusion.bootstrap.BootstrapServlet</servlet-class>
      	<init-param>
      		<param-name>servlet.class</param-name>
      		<param-value>org.apache.axis.transport.http.AxisServlet</param-value>
      	</init-param>
      	<load-on-startup>101</load-on-startup>
    </servlet>
	<!-- end WSRP -->
    
    <!-- start flex2
    <servlet-mapping>
        <servlet-name>FlexMxmlServlet</servlet-name>
        <url-pattern>*.mxml</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FlexInternalServlet</servlet-name>
        <url-pattern>/flex-internal/*</url-pattern>
    </servlet-mapping>    
    end flex2 -->

    <servlet-mapping id="coldfusion_mapping_3">
        <servlet-name>CfmServlet</servlet-name>
        <url-pattern>*.cfm</url-pattern>
    </servlet-mapping>
    <servlet-mapping id="coldfusion_mapping_4">
        <servlet-name>CFCServlet</servlet-name>
        <url-pattern>*.cfc</url-pattern>
    </servlet-mapping>
    <servlet-mapping id="coldfusion_mapping_5">
        <servlet-name>CfmServlet</servlet-name>
        <url-pattern>*.cfml</url-pattern>
    </servlet-mapping>
    <!--
    If your J2EE server supports it, you can uncomment these.
    They are included to support Search Engine Safe (SES) URL types. 
    Some servers (Tomcat, SunONE) don't support wildcard mappings
    -->
  <!-- begin SES
  <servlet-mapping id="coldfusion_mapping_6">
     <servlet-name>CfmServlet</servlet-name>
     <url-pattern>*.cfml/*</url-pattern>
  </servlet-mapping>
  <servlet-mapping id="coldfusion_mapping_7">
    <servlet-name>CfmServlet</servlet-name>
    <url-pattern>*.cfm/*</url-pattern>
  </servlet-mapping>
  <servlet-mapping id="coldfusion_mapping_8">
    <servlet-name>CFCServlet</servlet-name>
    <url-pattern>*.cfc/*</url-pattern>
  </servlet-mapping>
  end SES -->
    <!-- begin RDS -->
    <servlet-mapping id="coldfusion_mapping_9">
        <servlet-name>RDSServlet</servlet-name>
        <url-pattern>/CFIDE/main/ide.cfm</url-pattern>
    </servlet-mapping>
    <!-- end RDS -->
	<!-- begin JS Debug -->
    <servlet-mapping id="coldfusion_mapping_19">
        <servlet-name>JSDebugServlet</servlet-name>
        <url-pattern>/JSDebugServlet/*</url-pattern>
    </servlet-mapping>
	<!-- end JS Debug -->
	<!-- begin JWS -->
    <servlet-mapping id="coldfusion_mapping_10">
        <servlet-name>CFCServlet</servlet-name>
        <url-pattern>*.jws</url-pattern>
    </servlet-mapping>
	<!-- end JWS -->
	<!-- begin CF Reporting -->
    <servlet-mapping id="coldfusion_mapping_12">
        <servlet-name>CFCServlet</servlet-name>
        <url-pattern>*.cfr</url-pattern>
    </servlet-mapping>
	<!-- end CF Reporting -->
    <servlet-mapping id="coldfusion_mapping_14">
        <servlet-name>CFFileServlet</servlet-name>
        <url-pattern>/CFFileServlet/*</url-pattern>
    </servlet-mapping>
	<servlet-mapping id="coldfusion_mapping_15">
        <servlet-name>CFRestServlet</servlet-name>
        <url-pattern>/rest/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping id="coldfusion_mapping_16">
        <servlet-name>CFRestServlet</servlet-name>
        <url-pattern>/api/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>CFForbiddenServlet</servlet-name>
        <url-pattern>*.hbmxml</url-pattern>
    </servlet-mapping>
    
   <!--  WSRP producer servlet mapping.. -->
   <!-- begin WSRP -->
   <servlet-mapping>
        <servlet-name>WSRPProducer</servlet-name>
        <url-pattern>/WSRPProducer/*</url-pattern>
   </servlet-mapping>
   <!-- end WSRP -->
    


    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <welcome-file-list id="WelcomeFileList_1034013110672">
        <welcome-file>index.cfm</welcome-file>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.htm</welcome-file>
    </welcome-file-list>

	<!-- secure profile start 
	<error-page>
	   <error-code>404</error-code>
	   <location>/error404.html</location>
	</error-page>
    secure profile end -->

    <!-- start flex2
    <taglib>
        <taglib-uri>FlexTagLib</taglib-uri>
        <taglib-location>/WEB-INF/lib/cf-bootstrap-for-flex.jar</taglib-location>
    </taglib>
    end flex2 -->

    <!-- for WebSphere deployment, please uncomment -->
    <!-- Start WebSphere deploy
    <resource-ref>
        <description>Flex Messaging WorkManager</description>
        <res-ref-name>wm/MessagingWorkManager</res-ref-name>
        <res-type>com.ibm.websphere.asynchbeans.WorkManager</res-type>
        <res-auth>Container</res-auth>
        <res-sharing-scope>Shareable</res-sharing-scope>
    </resource-ref>
    End WebSphere deploy -->

</web-app>

oi-select{display:block;position:relative;width:100%}oi-select .select-search{cursor:text;border:1px solid #d9d9d9;background-color:#fff;overflow:hidden!important;-moz-box-sizing:border-box;box-sizing:border-box;border-radius:4px}oi-select .select-search-list{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-flex-flow:row wrap;-ms-flex-flow:row wrap;flex-flow:row wrap;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;margin:0;padding:2px 4px;list-style:none}oi-select .select-search-list-item{font-size:14px;margin:2px 4px 2px 0;vertical-align:middle;white-space:normal}oi-select .select-search-list-item_selection{cursor:pointer;background:#efefef;border-color:#ebebeb}oi-select .select-search-list-item_selection:hover{border-color:#e5e5e5}oi-select .select-search-list-item_selection.focused,oi-select .select-search-list-item_selection:active{border:1px solid #fff;border-radius:0;box-shadow:inset 0 0 10px 5px #fff}oi-select .select-search-list-item_selection.focused .close{display:none}oi-select .select-search-list-item_selection-remove{padding-left:2px}oi-select .select-search-list-item_loader{float:right;margin-top:6px;width:16px;height:16px;background:url(data:image/gif;base64,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) top}oi-select .select-search-list-item_input{-webkit-box-flex:1;-webkit-flex-grow:1;-moz-box-flex:1;-ms-flex-positive:1;flex-grow:1;margin:4px 0 5px 5px}oi-select .select-search-list-item_input input{padding:0;outline:0;border:0;width:100%}oi-select .select-search-list-item_hide{position:fixed;width:0;height:0;margin:0;opacity:0;pointer-events:none;text-indent:-9999em}oi-select .select-dropdown{position:absolute;width:inherit;overflow-y:scroll;max-height:100px;min-width:160px;font-size:14px;background-color:#fff;border-radius:0 0 4px 4px;border:1px solid #66afe9;border-top:0;outline:0;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);background-clip:padding-box;z-index:1000}oi-select .select-dropdown-optgroup{margin:0;padding:0}oi-select .select-dropdown-optgroup-header{font-weight:bolder;padding:3px 10px}oi-select .select-dropdown-optgroup-option{padding:3px 20px}oi-select .select-dropdown-optgroup-option.active:not(.disabled){background-color:#f1f1f1;cursor:pointer}oi-select .select-dropdown-optgroup-option.disabled{color:#aaa}oi-select:not(.multiple) .select-search-list-item_selection{color:#000;width:100%;border-color:#fff;text-align:left}oi-select:not(.multiple) .select-search-list-item_selection:not(:active){background:0 0}oi-select:not(.multiple):not(.cleanMode) .select-search-list-item_selection-remove{display:none}oi-select:not(.multiple):not(.cleanMode) .select-search:after{content:"";position:absolute;display:block;right:10px;width:0;height:0;margin-top:-19px;border-color:#000 transparent transparent;border-style:solid;border-width:5px 5px 0}oi-select[disabled=disabled] .select-search{cursor:not-allowed;background:#eee;border:1px solid #bdbdbd;opacity:.5}oi-select[disabled=disabled] .select-search-list-item_selection{cursor:not-allowed;box-shadow:none;border-color:transparent}oi-select[disabled=disabled] .select-search-list-item_selection-remove{visibility:hidden}oi-select[disabled=disabled] .select-search-list-item_input input:disabled{cursor:not-allowed;background:0 0}oi-select.focused .select-search{border-color:#66afe9;outline:0;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)}oi-select.invalid-item .select-dropdown,oi-select.invalid-item .select-search,oi-select.limited .select-dropdown,oi-select.limited .select-search{border-color:#f1bc28;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(230,189,46,.6)}oi-select.open .select-search{border-radius:4px 4px 0 0;border-bottom:1px solid rgba(0,0,0,.075)}oi-select.open:not(.multiple) .select-search:after{border-color:transparent transparent #000;border-width:0 5px 5px}oi-select.loading:not(.multiple) .select-search:after{border-width:0}
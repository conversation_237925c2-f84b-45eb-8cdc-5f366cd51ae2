/*@import url(http://fonts.googleapis.com/css?family=Oxygen:400,700);*/
body,input 		{font-family: Oxygen, Verdana; font-size: 11px;}
#fmtopbar		{background-color: #C0C0C0; color: #000000; -webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px; font-weight: bold;
padding: 5px;}
#fmclose 		{float:right; padding: 0;margin: 0; cursor: pointer; }
#fmbreadcrumb, .fftoleft	{float: left;}
#fmbreadcrumb strong {border-bottom: white 1px dotted;}
#fmbreadcrumb .plain {border-bottom:0;}

.clear 			{clear: both;}
.fmtabslink 	{cursor: pointer;}
#toolbar		{height:26px; background:#D3D3D3; margin-top:10px; padding-left: 5px; margin-right:5px; margin-left:5px;-webkit-border-radius: 5px;border-radius: 5px;}
#holderbox		{overflow-y:auto; margin-top:10px; position: relative;}
#fmiconbar 		{float: right; display: block; font-size: 11px; padding-top: 5px;padding-right: 5px; top:-6; position: relative;}
#fmiconbar span {padding-left: 3px;padding-right: 3px; display: none; cursor: pointer;}
#fmiconbar .fmselected {color: #21A03E; background-color: #21A03E; color: #fff; -webkit-border-radius: 3px; border-radius: 3px;}

.fmd			{width:102px; position: relative; padding:2px; text-align:center; height:102px; overflow:hidden; margin:5px; float:left; cursor:pointer; float:left; border: 2px solid #E1E3DD; ; -webkit-border-radius: 5px; border-radius: 5px;}
.fmi			{width:102px; position: relative; padding:2px;  text-align:center; height:102px;  border: 2px solid #F4F4F4; cursor:pointer; overflow:hidden; margin:5px; float:left;  -webkit-border-radius: 5px; border-radius: 5px;}
.fmname			{display:block; height: 11px;width: 98px;  color: #676767; font-size:10px; font-family:Arial, Helvetica, sans-serif; bottom: 0; padding-bottom: 4px; text-align: center; overflow: hidden; position: absolute;}
.fmi:hover,.fmd:hover 		{background-color: #FBD55A; border-color: #FBD55A;}
.fmimg			{border:1px solid #EAEAD5; background:#FFF; padding:2px;  max-width:92px; max-height:82px; -webkit-border-radius: 3px; border-radius: 3px;}
.fmname			{display:block;  padding-top:5px; }
.fmselect		{color:#069; border: 2px solid #98C1EB;}


/* slider */
td img 				{ vertical-align: top; }
.input {-webkit-border-radius: 3px; border-radius: 3px; border: 1px solid #D0C4CD; padding: 3px;}
.fmslider			{display:none; position:absolute; z-index: 100; margin-left: 20px; } /* width assigned in js file */
.fmslider table 	{width: 100%;}
.fmslidersdow		{background-image:url(images/sliderbtmbg.png); background-position:top; background-repeat: repeat-x;}
.fmsliderlft		{background-image:url(images/sliderleft.png); vertical-align: top;}
.sliderright		{background-image:url(images/sliderright.png);}
.fmslidemid			{padding:10px; background:#EDEBE6; background-image:url(images/fmboxbg.gif); background-position:top; background-repeat:repeat-x}
.fmslider form		{display:inline; padding:0px; margin:0px}
.closebtn			{background-image:url(images/closebtn.gif); float: right; cursor:pointer; width:19px; height:18px; background-position:top; background-color: transparent; margin:0px; vertical-align: middle; padding-right:5px; padding-left:0px; padding-top:0px; padding-bottom:0px; border:0px;}
.closebtn:hover		{background-image:url(images/closebtn1.gif)}
.buttonblue 		{font-family:Verdana, Geneva, sans-serif; cursor:pointer; border: none; padding: 3px; padding-left: 10px; padding-right: 10px; color:#FFF; background-color: #699BC0; font-weight:bold; font-size:11px; -webkit-border-radius: 3px; border-radius: 3px; margin-left: 20px;}
.fmstates 			{margin-top: 5px;display: none;}

/* file manager : bubble */
.fmbubble			{background-image:url(images/buble.png); display:none; background-repeat:no-repeat; width:235px; height:108px; z-index:1002; position: absolute;}
.fmbubRight			{background-image:url(images/buble_rite.png);}
.fmbubclose			{padding:3px; cursor:pointer; top:0px; position:absolute; right:0px}
#fmbubinfo			{padding-left:20px; color:#999; position:absolute; bottom:5px}
#fmbubimg			{padding-left:20px; padding-top:10px}
.fmbubRight	.fmbubclose	{ margin-right:10px}

.fmbubbig			{width:58px; height:38px; cursor:pointer; display:none; margin-right:10px; }
.fmbublg			{background:url(images/large.png)}
.fmbubmid			{width:46px; height:30px; cursor:pointer; display:none; margin-right:10px; background:url(images/mid.png)}
.fmbubsm			{width:36px; height:23px; cursor:pointer; display:none; margin-right:10px; background:url(images/small.png)}
.fmbubdw			{background:url(images/download.png)}
.fmbubem			{background:url(images/embad.png)}
.fmbubexe			{background:url(images/execute.png)}
.fmbubcode			{background:url(images/code.png)}
.fmbublg:hover		{background:url(images/large_o.png)}
.fmbubmid:hover		{background:url(images/mid_o.png)}
.fmbubsm:hover		{background:url(images/small_o.png)}
.fmbubdw:hover		{background:url(images/download_o.png)}
.fmbubem:hover		{background:url(images/embad_o.png)}
.fmbubexe:hover		{background:url(images/execute_o.png)}
.fmbubcode:hover	{background:url(images/code_o.png)}
#fmbubhint			{padding-left:25px; padding-top:5px; color:#FFF; font-size:11px; font-family:Verdana, Geneva, sans-serif}
#fmbubinfo a		{color:#999;}
#fmbubinfo a:hover	{color:#CCC;}
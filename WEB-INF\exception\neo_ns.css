/*
Changed BGColor for Arizona
old color #F5F5DE;
new color #FFFFF0
*/



BODY ,  P {
	font-family: verdana, arial, helvetica, sans-serif;
	background-color: #FFFFF0;
	font-size: 14px;
}

PRE	{
	font-size: 14px;
	font-family: "courier new", courier, monospace;
	color: Maroon;
	background-color: #FFFFF0;
	margin-left: 40px;
}

span.codemark {	font-weight: bold;
								background-color : #D3D3D3;
								color :  Black;}
CODE {
	font-size: 14px;
	font-family: "courier new", courier, monospace;
	color: Maroon;
	background-color: #FFFFF0;
	display : inline;
}

		
P	{
	margin-left: 36px;
	margin-top: 6px;
	font-size: 14px;
}

ol		{
	margin-left: 36px;
	font-family: verdana, arial, helvetica, sans-serif;
	background-color: #FFFFF0;
	font-size: 14px;
}

ul		{
	margin-left: 24px;
	font-family: verdana, arial, helvetica, sans-serif;
	background-color: #FFFFF0;
	font-size: 14px;
}

ol	ul	{
	margin-left: 0px;
	font-family: verdana, arial, helvetica, sans-serif;
	background-color: #FFFFF0;
	font-size: 14px;
}

ul	ul	{
	margin-left: 0px;
	font-family: verdana, arial, helvetica, sans-serif;
	background-color: #FFFFF0;
	font-size: 14px;
}

td ul	{
	margin-left: 0px;
}
		
ul p 	{
	margin-left: 0px;
}

ol p 	{
	margin-left: 0px;
}

OL PRE {
	margin-left: 0px;
}

li	{
	margin-left: 0px;
	font-family: verdana, arial, helvetica, sans-serif;
	background-color: #FFFFF0;
	font-size: 14px;
}

H1  {
	font-size: 20px;
	font-family: verdana, arial, helvetica, sans-serif;
	color: black;
	background-color: #FFFFF0;
	font-weight: bold;
}

H2  {
	font-size: 18px;
	font-family: Verdana, Arial, sans-serif;
	margin-left: 12px;
	color: black;
	background-color: #FFFFF0;
	font-weight: bold;
}

H3	{
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 16px;
	color: black;
	background-color: #FFFFF0;
	font-weight: bold;
	margin-left: 24px;
}

	
H4	{
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 14px;
	font-weight: bold;
	color: black;
	background-color: #FFFFF0;
	margin-left: 36px;
}

H5  {
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 12px;
	font-weight: normal;
	color: black;
	background-color: #FFFFF0;
	margin-left: 36px;
}

/*Parameter list*/

H5.param  {
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 12px;
	font-weight: bold;
	color: black;
	background-color: #FFFFF0;
	margin-left: 40px;
	margin-bottom: 0px;
	margin-top:0px;
}	

P.param	{
	margin-left: 40px;
	margin-top: -14px;
	font-size: 14px;
}

/* Table styles*/

div {
	margin-left: 36px;
}

div.navigation
{ 
margin-left: 0px;
 }
 
 div.navigation p
{ 
margin-left: 0px;
 }
  
table { margin-top: 12px; } 

td {
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 12px;
	margin-left: 0px;
	/*vertical-align: top;*/
	text-align :  left;
}

	
th {
	font-family: verdana, arial, helvetica, sans-serif;
	font-size: 12px;
	font-weight: bold;
	background-color: #CCCCCC;
	margin-left: 0px;
	text-align :  left;
}

th p {
		font-size: 12px;
		background-color: #CCCCCC;
	 	text-align :  left;
 }
 
TD.proc {
	font-family: verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	font-size: 14px;
}

TD.copy {
	font-size: 10px;
	color: gray;
	background-color:  #FFFFF0; 
} 
			
	/* This works around soft returns in the syntax
Paragraph style */

pre br { display: none; }	

/*===================================
 This is where I started playing around with the 
 procedure icon */
 
div.proc { margin-left: 22px; }

li.first { 
			margin-top: -12px;
			margin-left: 0px;
			font-family: verdana, arial, helvetica, sans-serif;
			background-color: #FFFFF0;
			font-size: 14px;
			}

/* end of proc icon work
======================================*/

